# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

from oslo_config import cfg

from neutron._i18n import _

metering_agent_opts = [
    cfg.StrOpt('driver',
               default='neutron.services.metering.drivers.noop.'
               'noop_driver.NoopMeteringDriver',
               help=_("Metering driver")),
    cfg.BoolOpt('enable_monitor_vm', default=True,
               help=_("If set to True, will monitor vm health,"
                      "set False to stopping monitoring vm")),
    cfg.BoolOpt('enable_monitor_vpc', default=True,
                help=_("If set to True, will monitor vpc,"
                       "set False to stopping monitoring vpc")),
    cfg.BoolOpt('enable_monitor_router', default=True,
                help=_("If set to True, will monitor vm health,"
                       "set False to stopping monitoring router")),
    cfg.BoolOpt('enable_monitor_eip', default=True,
                help=_("If set to True, will monitor eip which "
                       "binding with vm and router,set False to "
                       "stopping monitoring router")),
    cfg.BoolOpt('enable_monitor_vpc2vpc', default=True,
                help=_("If set to True, will monitor vpc to vpc,"
                       "set False to stopping monitoring router")),
    cfg.IntOpt('measure_interval', default=10,
               help=_("Interval between two metering measures")),
    cfg.IntOpt('report_interval', default=180,
               help=_("Interval between two metering reports")),
    cfg.IntOpt('report_router_tcp_con_interval', default=300,
               help=_("Interval tcp connections in vrouter reports")),
    cfg.IntOpt('report_router_udp_con_interval', default=300,
               help=_("Interval udp connections in vrouter reports")),
    cfg.IntOpt('report_router_icmp_con_interval', default=300,
               help=_("Interval icmp connections in vrouter reports")),
    cfg.IntOpt('report_vm_interval', default=300,
               help=_("Interval between two vm connection status reports")),
    cfg.IntOpt('report_vpc2vpc_interval', default=900,
               help=_("Interval between two vpc2vpc status reports")),
    cfg.IntOpt('sync_router_vpc_interval', default=600,
               help=_("Interval of router and vpc2vpc db sync")),
    cfg.StrOpt('kafka_host',
               default='',
               help=_('The kafka server ip:port.')),
    cfg.StrOpt('monitor_topic_router',
               default='monitor_topic_router',
               help=_('topic name for reporting router counter')),
    cfg.StrOpt('monitor_topic_router_con_detail',
               default='monitor_topic_router_con_detail',
               help=_('topic name for reporting router connections detail')),
    cfg.StrOpt('monitor_topic_eip',
               default='monitor_topic_eip',
               help=_('topic name for reporting EIP counter')),
    cfg.StrOpt('monitor_topic_vpc',
               default='monitor_topic_vpc',
               help=_('topic name for reporting vpc counter')),
    cfg.StrOpt('monitor_topic_vpc2vpc',
               default='monitor_topic_vpc2vpc',
               help=_('topic name for reporting connection '
                      'status between vpcs')),
    cfg.StrOpt('monitor_topic_vm_healthchk',
               default='monitor_topic_vm_healthchk',
               help=_('topic name for reporting vm health check counter')),
    cfg.BoolOpt('enable_conn_tcp',
               default=True,
               help=_('enable statistic TCP pro of conntrack')),
    cfg.BoolOpt('enable_conn_udp',
               default=True,
               help=_('enable statistic UDP pro of conntrack')),
    cfg.BoolOpt('enable_conn_icmp',
               default=True,
               help=_('enable statistic ICMP pro of conntrack')),
    cfg.BoolOpt('enable_dvr_bridge', default=False,
                help=_("Enable dvr by openflow"))
]


def register_metering_agent_opts(cfg=cfg.CONF):
    cfg.register_opts(metering_agent_opts)
