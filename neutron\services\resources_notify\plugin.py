#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib.api.definitions import l3 as l3_apidef
from neutron_lib.callbacks import events
from neutron_lib.callbacks import exceptions
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import exceptions as n_exc
from neutron_lib.exceptions import l3 as l3_exc
from neutron_lib import constants
from neutron_lib.plugins import directory
from neutron_lib.plugins import constants as plugin_constants
from oslo_config import cfg
from oslo_log import log as logging

from neutron.api.rpc.callbacks import events as rpc_events
from neutron.api.rpc.handlers import resources_rpc
from neutron.conf.db import l3_db
from neutron.conf.db import l3_dvr_db
from neutron.conf.services import port_forwarding as port_forwarding_conf
from neutron.common import exceptions as common_exc
from neutron.common import utils
from neutron.db import _resource_extend as resource_extend
from neutron.db.models import l3 as l3_models
from neutron.db.models import l3_attrs
from neutron.db import models_v2 as models
from neutron.extensions import _resources_notify as api_def
from neutron.extensions import resources_notify as ext_resources_notify
from neutron.extensions import _elastic_snat as esnat_def
from neutron.objects import elastic_snat as elastic_snat_obj
from neutron.objects import port_forwarding as port_forwarding_obj
from neutron.objects import router as l3_obj
from neutron.objects import floatingip as l3_fip_obj
from neutron.services.portforwarding.common import exceptions as pf_exc

LOG = logging.getLogger(__name__)

l3_db.register_db_l3_opts()
l3_dvr_db.register_db_l3_dvr_opts()
port_forwarding_conf.register_elastic_snat_db_opts()
NO_VALID_HOST = "NO-VALID-HOST"


class ResourcesNotifyPlugin(ext_resources_notify.ResourcesNotifyPluginBase):
    supported_extension_aliases = [api_def.ALIAS]

    def __init__(self):
        super(ResourcesNotifyPlugin, self).__init__()
        self.handler_map = self._build_handler_map()
        self.push_api = resources_rpc.ResourcesPushRpcApi()
        self.core_plugin = directory.get_plugin()
        self.l3_plugin = directory.get_plugin(plugin_constants.L3)
        self.pf_plugin = directory.get_plugin(plugin_constants.PORTFORWARDING)
        self.elastic_snat_plugin = directory.get_plugin(esnat_def.ELASTIC_SNAT)

    def _build_handler_map(self):
        handler_map = {}
        for resource in api_def.RESOURCES:
            for event in api_def.EVENTS:
                method_name = "{}_{}".format(resource, event)
                handler_map[(resource, event)] = (
                    getattr(self, method_name, self.default_handler))
        return handler_map

    def resources_notify(self, context, body, **kwargs):
        resources_notify = body.get(api_def.RESOURCE_NAME)
        resource_type = resources_notify.get("resource_type")
        event_type = resources_notify.get("event_type")
        data = resources_notify.get("resource", {})

        handler = self.handler_map.get((resource_type, event_type),
                                       self.default_handler)

        return handler(context, data)

    def network_created(self, data):
        return {"message": "network created"}

    def network_updated(self, data):
        return {"message": "network updated"}

    def network_deleted(self, data):
        return {"message": "network deleted"}

    def subnet_created(self, data):
        return {"message": "subnet created"}

    def subnet_updated(self, data):
        return {"message": "subnet updated"}

    def subnet_deleted(self, data):
        return {"message": "subnet deleted"}

    def port_created(self, data):
        return {"message": "port created"}

    def port_updated(self, data):
        return {"message": "port updated"}

    def port_deleted(self, data):
        return {"message": "port deleted"}

    def flow_log_created(self, data):
        return {"message": "flow_log created"}

    def flow_log_updated(self, data):
        return {"message": "flow_log updated"}

    def flow_log_deleted(self, data):
        return {"message": "flow_log deleted"}

    def qos_policy_created(self, data):
        return {"message": "qos_policy created"}

    def qos_policy_updated(self, data):
        return {"message": "qos_policy updated"}

    def qos_policy_deleted(self, data):
        return {"message": "qos_policy deleted"}

    def qos_rule_created(self, data):
        return {"message": "qos_rule created"}

    def qos_rule_updated(self, data):
        return {"message": "qos_rule updated"}

    def qos_rule_deleted(self, data):
        return {"message": "qos_rule deleted"}

    def _raise_router_exception(self, msg):
        LOG.error(msg)
        raise n_exc.BadRequest(resource='router', msg=msg)

    def _update_router_gwinfo(self, context, router_db, gw_info):
        gw_port = router_db.gw_port
        ext_ips = gw_info.get('external_fixed_ips') if gw_info else []
        ext_ip_change = self.l3_plugin._check_for_external_ip_change(
            context, gw_port, ext_ips)
        network_id = self.l3_plugin._validate_gw_info(
            context, gw_port, gw_info, ext_ips)
        if not (gw_port and ext_ip_change and gw_port['network_id'] == network_id):
            new_valid_gw_port_attachment = (
                    network_id and (not router_db.gw_port or
                                    router_db.gw_port['network_id'] != network_id))
            if new_valid_gw_port_attachment:
                subnets = self.core_plugin.get_subnets_by_network(context,
                                                                  network_id)
                try:
                    kwargs = {'context': context, 'router_id': router_db['id'],
                              'network_id': network_id, 'subnets': subnets}
                    registry.notify(
                        resources.ROUTER_GATEWAY, events.BEFORE_CREATE, self,
                        **kwargs)
                except exceptions.CallbackFailure as e:
                    # raise the underlying exception
                    raise e.errors[0].error

    def router_created(self, context, data):
        router = data.get("router")
        router_db = self.l3_plugin._get_router(context, router['id'])
        new_router = self.l3_plugin._make_router_dict(router_db)
        gw_info = router.get(l3_apidef.EXTERNAL_GW_INFO, None)
        registry.notify(resources.ROUTER, events.BEFORE_CREATE,
                        self, context=context, router=router)
        registry.notify(resources.ROUTER, events.PRECOMMIT_CREATE,
                        self, context=context, router=router,
                        router_id=router['id'], router_db=router_db)
        if gw_info:
            self._update_router_gwinfo(context, router_db, gw_info)

        if self.l3_plugin._is_router_total_limits_supported:
            total_bandwidth, total_packet_rate, total_connection = \
                self.l3_plugin._get_router_total_limits(
                    context, router)
            new_router['total_bandwidth'] = total_bandwidth
            new_router['total_packet_rate'] = total_packet_rate
            new_router['total_connection'] = total_connection
        registry.notify(resources.ROUTER, events.AFTER_CREATE, self,
                        context=context, router_id=router_db.id,
                        router=new_router, request_attrs=router,
                        router_db=router_db)
        if (cfg.CONF.router_limits_scheduler_enable or
                new_router.get('external_gateway_info')):
            try:
                self.l3_plugin.notify_router_updated(context, new_router['id'], None)
            except common_exc.FailedToScheduleRouter:
                msg = 'No valid host found for router: %s' % new_router['id']
                self._raise_router_exception(msg)
        if (cfg.CONF.router_limits_scheduler_enable and
                new_router['status'] == NO_VALID_HOST):
            msg = 'No valid host found for router: %s' % new_router['id']
            self._raise_router_exception(msg)
        return {"message": "router created"}

    def router_updated(self, context, data):
        router = data.get("router")
        old_router = data.get("old_router")
        old_router_db = self._build_router_model(context, old_router)
        old_router_dict = self.l3_plugin._make_router_dict(old_router_db)
        new_router_db = self.l3_plugin._get_router(context, router['id'])
        # need to handle the port notification logic for gatewayinfo?
        gw_info = old_router.get(l3_apidef.EXTERNAL_GW_INFO, constants.ATTR_NOT_SPECIFIED)
        if gw_info != constants.ATTR_NOT_SPECIFIED:
            self._update_router_gwinfo(context, old_router_db, gw_info)

        registry.publish(resources.ROUTER, events.PRECOMMIT_UPDATE,
                         self, payload=events.DBEventPayload(
                            context, request_body={'router': router},
                            states=(old_router_dict,), resource_id=router['id'],
                            desired_state=new_router_db))
        if 'ha' in router or 'distributed' in router:
            self.l3_plugin.after_migration(context, old_router_dict, new_router_db)
        updated = self.l3_plugin._make_router_dict(new_router_db)
        if self.l3_plugin._is_router_total_limits_supported:
            if ('total_bandwidth' in router) or (
                    'total_packet_rate' in router) or (
                    'total_connection' in router):
                total_bandwidth, total_packet_rate, total_connection = \
                    self.l3_plugin._get_router_total_limits(
                        context, router)
                updated['total_bandwidth'] = total_bandwidth
                updated['total_packet_rate'] = total_packet_rate
                updated['total_connection'] = total_connection
        registry.notify(resources.ROUTER, events.AFTER_UPDATE, self,
                        context=context, router_id=router['id'], old_router=old_router_dict,
                        router=updated, request_attrs={'router': router}, router_db=new_router_db)
        try:
            self.l3_plugin.notify_router_updated(context, router['id'], None)
        except common_exc.FailedToScheduleRouter:
            msg = 'No valid host found for router: %s' % router['id']
            self._raise_router_exception(msg)
        return {"message": "router updated"}

    def _build_router_model(self, context, router):
        router_db = l3_models.Router(
            id=router['id'],
            tenant_id=router['tenant_id'],
            name=router['name'],
            admin_state_up=router['admin_state_up'],
            status=constants.ACTIVE,
            description=router.get('description'))
        gw_port = router.get('gw_port', None)
        if gw_port:
            fixed_ips = []
            for fixed_ip in gw_port.get('fixed_ips', []):
                ia = models.IPAllocation(
                    subnet_id=fixed_ip.get('subnet_id'),
                    ip_address=fixed_ip.get('ip_address'))
                fixed_ips.append(ia)
            router_db.gw_port = models.Port(
                id=gw_port.get('id'),
                network_id=gw_port.get('network_id'),
                device_id=router['id'],
                device_owner=gw_port.get('device_owner'),
                status=gw_port.get('status'),
                admin_state_up=gw_port.get('admin_state_up'),
                fixed_ips=fixed_ips
            )

        # 2. extra_attributes
        if any(k in router for k in
               ['distributed', 'ha', 'service_router',
                'availability_zone_hints',
                'custom_type', 'cloud_attributes']):
            custom_type = None
            cloud_attributes = None
            if router.get('custom_type'):
                try:
                    custom_type = router['custom_type']
                    if isinstance(custom_type, list):
                        custom_type = utils.convert_list_to_string(router['custom_type'])
                except Exception as e:
                    LOG.error("Failed to process custom_type: %s", e)
            if router.get('cloud_attributes'):
                try:
                    cloud_attrs = router['cloud_attributes']
                    if isinstance(cloud_attrs, dict):
                        cloud_attributes = utils.filter_to_json_str(cloud_attrs)
                except Exception as e:
                    LOG.error("Failed to process cloud_attributes: %s", e)
            extra_attributes = l3_attrs.RouterExtraAttributes(
                router_id=router['id'],
                distributed=router.get('distributed', False),
                service_router=router.get('service_router', False),
                ha=router.get('ha', False),
                ha_vr_id=router.get('ha_vr_id', 0),
                availability_zone_hints=router.get('availability_zone_hints'),
                custom_type=custom_type,
                cloud_attributes=cloud_attributes
            )

            router_db.extra_attributes = extra_attributes

        # 3. routes
        if 'routes' in router:
            route_list = []
            for route_dict in router['routes']:
                destination = route_dict.get('destination')
                nexthop = route_dict.get('nexthop')
                route = l3_models.RouterRoute(
                    router_id=router['id'],
                    destination=str(destination),
                    nexthop=str(nexthop)
                )
                route_list.append(route)
            router_db.route_list = route_list

        # 4. RouterTotalLimits
        if any(k in router for k in ['total_bandwidth', 'total_packet_rate', 'total_connection']):
            router_limits = l3_models.RouterTotalLimits(
                router_id=router['id'],
                total_bandwidth=router.get('total_bandwidth'),
                total_packet_rate=router.get('total_packet_rate'),
                total_connection=router.get('total_connection')
            )
            router_db.router_total_limits = router_limits
        return router_db

    def router_deleted(self, context, data):
        router = data.get("router")

        registry.publish(resources.ROUTER, events.BEFORE_DELETE, self,
                         payload=events.DBEventPayload(
                             context, resource_id=router['id']))

        router_db = self._build_router_model(context, router)
        original = self.l3_plugin._make_router_dict(router_db)
        port_requires_deletion = (
                router_db.gw_port and router_db.gw_port['network_id'] != None)
        if port_requires_deletion:
            gw_ips = [x['ip_address'] for x in router_db.gw_port['fixed_ips']]
            registry.notify(resources.ROUTER_GATEWAY,
                            events.AFTER_DELETE, self,
                            router_id=router['id'],
                            context=context,
                            router=router_db,
                            network_id=router_db.gw_port['network_id'],
                            new_network_id=None,
                            gateway_ips=gw_ips)
        registry.notify(resources.ROUTER, events.PRECOMMIT_DELETE,
                        self, context=context, router_db=router_db,
                        router_id=router['id'], original=original)

        try:
            registry.publish(resources.ROUTER_GATEWAY,
                             events.BEFORE_DELETE, self,
                             payload=events.DBEventPayload(
                                 context, states=(router_db,),
                                 resource_id=router['id']))
        except exceptions.CallbackFailure as e:
                # NOTE(armax): preserve old check's behavior
                if len(e.errors) == 1:
                    raise e.errors[0].error
                raise l3_exc.RouterInUse(router_id=router['id'], reason=e)

        registry.notify(resources.ROUTER, events.AFTER_DELETE, self,
                        context=context, router_id=router['id'], original=original)
        self.l3_plugin.notify_router_deleted(context, router['id'])
        return {"message": "router deleted"}

    def router_interface_created(self, context, data):
        router_interface = data.get("router_interface")
        self.l3_plugin.notify_router_interface_action(
            context, router_interface, 'add')
        return {"message": "router_interface created"}

    def router_interface_updated(self, context, data):
        router_interface = data.get("router_interface")
        router_id = data.get("router_id")
        registry.notify(resources.ROUTER_INTERFACE,
                        events.AFTER_UPDATE,
                        self,
                        context=context,
                        router_id=router_id,
                        interface_info=router_interface)
        return {"message": "router_interface updated"}

    def router_interface_deleted(self, context, data):
        router_interface = data.get("router_interface")
        self.l3_plugin.notify_router_interface_action(
            context, router_interface, 'remove')
        return {"message": "router_interface deleted"}

    def floating_ip_created(self, context, data):
        floatingip = data.get("floatingip")
        try:
            # request_body need {'floatingip': {'id': 'xxx', 'xxx': 'xxx'}}
            registry.publish(resources.FLOATING_IP, events.BEFORE_CREATE,
                             self, payload=events.DBEventPayload(
                    context, request_body={'floatingip': floatingip}))
        except exceptions.CallbackFailure as e:
            # raise the underlying exception
            raise e.errors[0].error
        fip_id = floatingip['id']
        floatingip_obj = self.l3_plugin._get_floatingip(context,
                                                        fip_id)
        floatingip_db = floatingip_obj.db_obj
        floatingip_dict = self.l3_plugin._make_floatingip_dict(
            floatingip_obj, process_extensions=False)
        if self.l3_plugin._is_dns_integration_supported:
            floatingip_dict['dns_domain'] = floatingip.get('dns_domain')
            floatingip_dict['dns_name'] = floatingip.get('dns_name')
        registry.notify(resources.FLOATING_IP, events.PRECOMMIT_CREATE,
                        self, context=context, floatingip=floatingip,
                        floatingip_id=fip_id,
                        floatingip_db=floatingip_db)
        port_id = internal_ip_address = router_id = None
        if floatingip.get('port_id'):
            port_id, internal_ip_address, router_id = self.l3_plugin._get_assoc_data(
                context, floatingip, floatingip_obj)
        floating_ip_address = (str(floatingip_obj.floating_ip_address)
                               if floatingip_obj.floating_ip_address else None)
        assoc_data = {'fixed_ip_address': internal_ip_address,
                      'fixed_port_id': port_id,
                      'router_id': router_id,
                      'last_known_router_id': floatingip_obj.router_id,
                      'floating_ip_address': floating_ip_address,
                      'floating_network_id': floatingip_obj.floating_network_id,
                      'floating_ip_id': floatingip_obj.id,
                      'context': context,
                      'admin_state_up': floatingip_obj.admin_state_up,
                      'fip_type': floatingip_obj.fip_type}
        registry.notify(resources.FLOATING_IP,
                        events.AFTER_UPDATE,
                        self.l3_plugin._update_fip_assoc,
                        **assoc_data)

        floatingip_dict['allowed_port_numbers'] = (floatingip['allowed_port_numbers']
                                  if 'allowed_port_numbers' in floatingip else [])
        floatingip_dict['denied_port_numbers'] = (floatingip['denied_port_numbers']
                                 if 'denied_port_numbers' in floatingip else [])
        resource_extend.apply_funcs(l3_apidef.FLOATINGIPS, floatingip_dict,
                                    floatingip_db)
        self.l3_plugin._notify_floating_ip_change(context, floatingip_dict)
        return {"message": "floating_ip created"}

    def _filter_to_str(self, value):
        if isinstance(value, list):
            return [str(val) for val in value]
        return str(value)

    def _build_floating_ip_object(self, context, floatingip):
        if not floatingip:
            raise n_exc.BadRequest(resource='floatingip',
                                 msg='FloatingIP data is required')

        fip = floatingip

        fip_id = fip.get('id')

        floating_ip_address = fip.get('floating_ip_address')
        if floating_ip_address:
            floating_ip_address = netaddr.IPAddress(floating_ip_address)

        external_port = None
        if fip.get('floating_port_id'):
            external_port = {
                'id': fip.get('floating_port_id'),
                'network_id': fip.get('floating_network_id'),
                'device_id': fip_id,
                'device_owner': constants.DEVICE_OWNER_FLOATINGIP,
                'admin_state_up': True,
                'status': constants.PORT_STATUS_NOTAPPLICABLE,
                'tenant_id': '',
                'name': '',
                'fixed_ips': []
            }

            if floating_ip_address:
                external_port['fixed_ips'] = [{
                    'ip_address': str(floating_ip_address),
                    'subnet_id': fip.get('floating_subnet_id')
                }]


        # bulid floatingip object
        floatingip_obj = l3_obj.FloatingIP(
            context,
            id=fip_id,
            project_id=fip.get('tenant_id') or fip.get('project_id'),
            floating_ip_address=floating_ip_address,
            floating_network_id=fip.get('floating_network_id'),
            floating_port_id=fip.get('floating_port_id'),
            status=fip.get('status', constants.FLOATINGIP_STATUS_ACTIVE),
            fip_type=fip.get('fip_type', constants.FLOATINGIP_TYPE_FIP),
            description=fip.get('description')
        )
        previous_router_id = floatingip_obj.router_id
        has_new_assoc = True if fip.get('port_id') else False
        port_id = internal_ip_address = router_id = None
        if fip.get('port_id'):
            port_id, internal_ip_address, router_id = self.l3_plugin._get_assoc_data(
                context,
                fip,
                floatingip_obj)
        floatingip_obj.fixed_ip_address = (
            netaddr.IPAddress(internal_ip_address)
            if internal_ip_address else None)
        floatingip_obj.fixed_port_id = port_id
        floatingip_obj.router_id = router_id
        floatingip_obj.last_known_router_id = previous_router_id
        if 'description' in fip:
            floatingip_obj.description = fip['description']
        if has_new_assoc:
            floatingip_obj.admin_state_up = True
        elif not floatingip_obj.router_id:
            floatingip_obj.admin_state_up = False
        if 'admin_state_up' in fip and not has_new_assoc:
            floatingip_obj.admin_state_up = fip['admin_state_up']


        if fip.get('allowed_port_numbers') or fip.get('denied_port_numbers'):
            fip_ad_ports_data = {
                'floatingip_id': fip_id,
                'allowed_port_numbers': fip.get('allowed_port_numbers', []),
                'denied_port_numbers': fip.get('denied_port_numbers', [])
            }

            fip_ad_ports_obj = l3_fip_obj.FipADPortsLists(
                context, **fip_ad_ports_data
            )

            floatingip_obj.fip_ad_ports_lists = fip_ad_ports_obj

        if fip.get('dns_name') or fip.get('dns_domain'):
            dns_data = {
                'floatingip_id': fip_id,
                'dns_name': fip.get('dns_name', ''),
                'dns_domain': fip.get('dns_domain', ''),
                'published_dns_name': fip.get('published_dns_name', ''),
                'published_dns_domain': fip.get('published_dns_domain', '')
            }

            fip_dns_obj_instance = l3_fip_obj.FloatingIPDNS(
                context, **dns_data
            )
            floatingip_obj.dns = fip_dns_obj_instance

        floatingip_dict = self.l3_plugin._make_floatingip_dict(
            floatingip_obj, process_extensions=False)

        floatingip_dict['allowed_port_numbers'] = fip.get('allowed_port_numbers', [])
        floatingip_dict['denied_port_numbers'] = fip.get('denied_port_numbers', [])

        if fip.get('dns_name'):
            floatingip_dict['dns_name'] = fip.get('dns_name')
        if fip.get('dns_domain'):
            floatingip_dict['dns_domain'] = fip.get('dns_domain')

        assoc_data = {'fixed_ip_address': internal_ip_address,
                'fixed_port_id': port_id,
                'router_id': router_id,
                'last_known_router_id': previous_router_id,
                'floating_ip_address': floating_ip_address,
                'floating_network_id': floatingip_obj.floating_network_id,
                'floating_ip_id': floatingip_obj.id,
                'context': context,
                'admin_state_up': floatingip_obj.admin_state_up,
                'fip_type': floatingip_obj.fip_type}

        result = {
            'floatingip_obj': floatingip_obj,
            'floatingip_dict': floatingip_dict,
            'external_port': external_port,
            'assoc_data': assoc_data
        }

        LOG.debug("Successfully built FloatingIP object in memory: %s",
                 {'id': fip_id, 'floating_ip_address': str(floating_ip_address) if floating_ip_address else None})

        return result

    def floating_ip_updated(self, context, data):
        # Ensure that both project_id and tenant_id
        # attributes are present.
        old_floatingip = data.get("old_floatingip")
        floatingip = data.get("floatingip")
        try:
            # request_body need {'floatingip': {'id': 'xxx', 'xxx': 'xxx'}}
            registry.publish(resources.FLOATING_IP, events.BEFORE_UPDATE,
                             self, payload=events.DBEventPayload(
                                context,
                                request_body={'floatingip': floatingip},
                                resource_id=floatingip['id']))
        except exceptions.CallbackFailure as e:
            # raise the underlying exception
            raise e.errors[0].error

        # Need old_floatingip object transfer to dict.
        # Ensure that both project_id and tenant_id
        # attributes are present.
        old_floatingip = data.get("old_floatingip")
        fip_id = floatingip['id']
        floatingip_obj = self.l3_plugin._get_floatingip(context,
                                                        fip_id)
        external_port = self.core_plugin.get_port(
            context.elevated(), floatingip_obj.floating_port_id)
        assoc_result = self.l3_plugin._update_fip_assoc(
            context, floatingip, floatingip_obj, external_port)
        registry.notify(resources.FLOATING_IP,
                        events.PRECOMMIT_UPDATE,
                        self,
                        floatingip={'floatingip': floatingip},
                        floatingip_db=floatingip_obj.db_obj,
                        old_floatingip=old_floatingip,
                        **assoc_result)

        registry.notify(resources.FLOATING_IP,
                        events.AFTER_UPDATE,
                        self.l3_plugin._update_fip_assoc,
                        **assoc_result)
        self.l3_plugin._notify_floating_ip_change(context, old_floatingip)
        if (floatingip['router_id'] != old_floatingip['router_id'] or
                floatingip['port_id'] != old_floatingip['port_id']):
            self.l3_plugin._notify_floating_ip_change(context, floatingip)
        return {"message": "floating_ip updated"}

    def floating_ip_deleted(self, context, data):
        # Please call this API first before deleting the object.
        floatingip = data.get("floatingip")
        # fip_id = floatingip['id']
        # floatingip_obj = self.l3_plugin._get_floatingip(context,
        #                                                 fip_id)
        # floatingip_dict = self.l3_plugin._make_floatingip_dict(floatingip_obj)
        try:
            # request_body need {'floatingip': {'id': 'xxx', 'xxx': 'xxx'}}
            registry.publish(resources.FLOATING_IP, events.BEFORE_DELETE,
                             self, payload=events.DBEventPayload(
                    context,
                    request_body=floatingip,
                    resource_id=floatingip['id']))
        except exceptions.CallbackFailure as e:
            # raise the underlying exception
            raise e.errors[0].error
        registry.notify(resources.FLOATING_IP, events.AFTER_DELETE,
                        self, **floatingip)
        self.l3_plugin._notify_floating_ip_change(context, floatingip)
        return {"message": "floating_ip deleted"}

    def _get_pf_fip_object(self, context, port_forwarding):
        floatingip_id = port_forwarding['floatingip_id']
        pf_obj = port_forwarding_obj.PortForwarding.get_object(
            context, id=port_forwarding["id"])
        if not pf_obj:
            raise pf_exc.PortForwardingNotFound(id=port_forwarding["id"])
        fip_obj = self.pf_plugin._get_fip_obj(context, floatingip_id)
        return pf_obj, fip_obj

    def port_forwarding_created(self, context, data):
        port_forwarding = data.get("port_forwarding")
        pf_obj, fip_obj = self._get_pf_fip_object(context, port_forwarding)
        router_id = self.pf_plugin._find_a_router_for_fip_port_forwarding(
            context, port_forwarding, fip_obj)
        self.push_api.push(context, [pf_obj], rpc_events.CREATED)
        # for metering
        if not cfg.CONF.is_sdn_arch and cfg.CONF.enable_meter_full_eip:
            registry.notify(port_forwarding_obj.PortForwarding.obj_name(),
                            events.AFTER_CREATE,
                            self, context=context,
                            project_id=fip_obj.project_id, pf_obj=pf_obj)
            LOG.debug("Notify create metering with "
                      "pf_obj=%s, project_id=%s",
                      pf_obj, fip_obj.project_id)

        self.l3_plugin.notify_router_updated(context, router_id)
        return {"message": "port_forwarding created"}

    def port_forwarding_updated(self, context, data):
        port_forwarding = data.get("port_forwarding")
        pf_obj, fip_obj = self._get_pf_fip_object(context, port_forwarding)
        self.push_api.push(context, [pf_obj], rpc_events.UPDATED)
        internal_ip_address = port_forwarding.get(
            'internal_ip_address')
        internal_port = port_forwarding.get('internal_port')
        protocol = port_forwarding.get('protocol')
        old_pf = data.get("old_port_forwarding")
        old_pf_obj = port_forwarding_obj.PortForwarding(context, **old_pf)
        # for metering
        if not cfg.CONF.is_sdn_arch and cfg.CONF.enable_meter_full_eip:
            registry.notify(port_forwarding_obj.PortForwarding.obj_name(),
                            events.AFTER_UPDATE,
                            self, context=context,
                            project_id=fip_obj.project_id,
                            pf_obj=old_pf_obj,
                            the_latest_ip_address=internal_ip_address,
                            the_larest_internal_port=internal_port,
                            the_latest_protocol=protocol)
            LOG.debug("Notify update metering with "
                      "new pf_obj=%s, project_id=%s",
                      pf_obj, fip_obj.project_id)
        return {"message": "port_forwarding updated"}

    def port_forwarding_deleted(self, context, data):
        # Please call this API first before deleting the object.
        port_forwarding = data.get("port_forwarding")
        pf_obj, fip_obj = self._get_pf_fip_object(context, port_forwarding)
        router_id = self.pf_plugin._find_a_router_for_fip_port_forwarding(
            context, port_forwarding, fip_obj)
        self.l3_plugin.notify_router_updated(context, router_id)
        self.push_api.push(context, [pf_obj], rpc_events.DELETED)
        # for metering
        if not cfg.CONF.is_sdn_arch and cfg.CONF.enable_meter_full_eip:
            registry.notify(port_forwarding_obj.PortForwarding.obj_name(),
                            events.AFTER_DELETE,
                            self, context=context,
                            project_id=fip_obj.project_id, pf_obj=pf_obj)
            LOG.debug("Notify delete metering with "
                      "pf_obj=%s, project_id=%s",
                      pf_obj, fip_obj.project_id)
        return {"message": "port_forwarding deleted"}

    def elastic_snat_created(self, context, data):
        elastic_snat = data.get("elastic_snat")
        esnat_obj = self.elastic_snat_plugin._get_elastic_snat(
            context, elastic_snat["id"])
        registry.publish(esnat_def.ELASTIC_SNAT, events.AFTER_CREATE, self,
                         payload=events.DBEventPayload(
                             context, states=(esnat_obj,),
                             resource_id=esnat_obj.id))
        self.elastic_snat_plugin.driver.create_elastic_snat(context, esnat_obj)
        return {"message": "elastic_snat created"}

    def elastic_snat_updated(self, context, data):
        elastic_snat = data.get("elastic_snat")
        esnat_obj = self.elastic_snat_plugin._get_elastic_snat(
            context, elastic_snat["id"])
        if ('subnets' in elastic_snat.keys()
                or 'internal_cidrs' in elastic_snat.keys()):
            registry.publish(esnat_def.ELASTIC_SNAT, events.AFTER_UPDATE, self,
                             payload=events.DBEventPayload(
                                 context, states=(esnat_obj,),
                                 resource_id=esnat_obj.id))
        self.elastic_snat_plugin.driver.update_elastic_snat(context, esnat_obj)
        return {"message": "elastic_snat updated"}

    def elastic_snat_deleted(self, context, data):
        # Please call this API first before deleting the object.
        elastic_snat = data.get("elastic_snat")
        esnat_obj = self.elastic_snat_plugin._get_elastic_snat(
            context, elastic_snat["id"])
        registry.publish(esnat_def.ELASTIC_SNAT, events.AFTER_DELETE, self,
                         payload=events.DBEventPayload(
                             context, states=(esnat_obj,),
                             resource_id=esnat_obj.id))
        self.elastic_snat_plugin.driver.delete_elastic_snat(context, esnat_obj)
        return {"message": "elastic_snat deleted"}

    def traffic_mirror_created(self, data):
        return {"message": "traffic_mirror created"}

    def traffic_mirror_updated(self, data):
        return {"message": "traffic_mirror updated"}

    def traffic_mirror_deleted(self, data):
        return {"message": "traffic_mirror deleted"}

    def security_group_created(self, data):
        return {"message": "security_group created"}

    def security_group_updated(self, data):
        return {"message": "security_group updated"}

    def security_group_deleted(self, data):
        return {"message": "security_group deleted"}

    def security_group_rule_created(self, data):
        return {"message": "security_group_rule created"}

    def security_group_rule_updated(self, data):
        return {"message": "security_group_rule updated"}

    def security_group_rule_deleted(self, data):
        return {"message": "security_group_rule deleted"}

    def default_handler(self, data):
        return {"error": "Unhandled event"}
