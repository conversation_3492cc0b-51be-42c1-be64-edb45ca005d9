#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import functools

import netaddr
from neutron_lib.api.definitions import l3 as l3_apidef
from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import constants as lib_consts
from neutron_lib.db import utils as db_utils
from neutron_lib.objects import exceptions as obj_exc
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from oslo_config import cfg
from oslo_db import exception as db_exc
from oslo_log import log as logging
from oslo_utils import importutils

from neutron.common import utils
from neutron.conf.services import elastic_snat as elastic_snat_conf
from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.db import db_base_plugin_common
from neutron.db.models import elastic_snat as es_db
from neutron.db.models import port_forwarding as pf_db
from neutron.extensions import _elastic_snat as api_def
from neutron.extensions import elastic_snat as ext_elastic_snat
from neutron.objects import base as base_obj
from neutron.objects import elastic_snat as elastic_snat_obj
from neutron.objects import router as router_obj
from neutron.services.elastic_snat.common import exceptions as esnat_exc

LOG = logging.getLogger(__name__)
elastic_snat_conf.register_elastic_snat_db_opts()

ELASTIC_SNAT_FLOATINGIP_KEY = '_elastic_snat_floatingips'


def make_result_with_fields(f):
    @functools.wraps(f)
    def inner(*args, **kwargs):
        fields = kwargs.get('fields')
        result = f(*args, **kwargs)
        if fields is None:
            return result
        elif isinstance(result, list):
            return [db_utils.resource_fields(r, fields) for r in result]
        else:
            return db_utils.resource_fields(result, fields)

    return inner


@resource_extend.has_resource_extenders
@registry.has_registry_receivers
class ElasticSnatPlugin(ext_elastic_snat.ElasticSnatPluginBase):

    supported_extension_aliases = [api_def.ALIAS]

    __native_pagination_support = True
    __native_sorting_support = True

    def __init__(self):
        super(ElasticSnatPlugin, self).__init__()
        self.l3_plugin = directory.get_plugin(constants.L3)
        self.core_plugin = directory.get_plugin()
        self.driver = importutils.import_object(
            cfg.CONF.elastic_snat_backend_api_driver)

    def _get_elastic_snat(self, context, id):
        obj = elastic_snat_obj.ElasticSnat.get_object(context, id=id)
        if obj is None:
            raise esnat_exc.ElasticSnatNotFound(id=id)
        return obj

    def _get_elastic_snat_count_for_router(self, context, router_id):
        return elastic_snat_obj.ElasticSnat.count(context, router_id=router_id)

    def _check_router_infos(self, context, router_id, subnets, internal_cidrs):
        filters = {'device_id': [router_id],
                   'device_owner': lib_consts.ROUTER_INTERFACE_OWNERS}
        interfaces = self.core_plugin.get_ports(
            context.elevated(), filters)
        router_subnets = []
        for inf in interfaces:
            for ip in inf['fixed_ips']:
                subnet = self.core_plugin.get_subnet(
                    context.elevated(),
                    id=ip['subnet_id'])
                router_subnets.append(subnet)
        # Check router subnets
        if subnets:
            interface_subnets = set()
            for rs in router_subnets:
                if rs['ip_version'] == lib_consts.IP_VERSION_4:
                    interface_subnets.add(rs['id'])
            if set(subnets) - interface_subnets:
                raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                    error="subnet(s) may not connect to router")

        # Check IP or CIDR in router subnets
        if internal_cidrs:
            interface_subnet_cidrs = set()
            tmp_internal_cidrs = set()
            cidrs_no_check = cfg.CONF.allow_cidrs_no_check_in_elastic_snat
            if cidrs_no_check:
                no_check_cidrs = [netaddr.IPNetwork(a) for a in cidrs_no_check]
                for cidr in internal_cidrs:
                    if all([netaddr.IPNetwork(cidr) not in net
                            for net in no_check_cidrs]):
                        tmp_internal_cidrs.add(cidr)
            else:
                for b in internal_cidrs:
                    tmp_internal_cidrs.add(b)
            if tmp_internal_cidrs:
                for rs in router_subnets:
                    if rs['ip_version'] == lib_consts.IP_VERSION_4:
                        interface_subnet_cidrs.add(
                            netaddr.IPNetwork(rs['cidr']))
                for cidr in tmp_internal_cidrs:
                    if all([netaddr.IPNetwork(cidr) not in network
                            for network in interface_subnet_cidrs]):
                        raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                            error="cidr(s) may be not in router subnet(s)")

    def check_cidr_overlaps(self, cidrs):
        length = len(cidrs)
        if cidrs and length == 1:
            return False
        for i in range(length):
            index_c = netaddr.IPNetwork(cidrs[i])
            for c in cidrs[:i]:
                index_j = netaddr.IPNetwork(c)
                if index_j in index_c or index_c in index_j:
                    return True
            for c in cidrs[i + 1:]:
                index_k = netaddr.IPNetwork(c)
                if index_k in index_c or index_k in index_c:
                    return True
        return False

    def check_cidr_in_list(self, cidr, check_list):
        index_c = netaddr.IPNetwork(cidr)

        for c in check_list:
            check_cidr = netaddr.IPNetwork(c)
            if check_cidr in index_c or index_c in check_cidr:
                return True
        return False

    def check_ip_overlaps(self, request_cidrs, existing_cidrs=None):
        if self.check_cidr_overlaps(request_cidrs):
            raise esnat_exc.FailedToCreateElasticSnatCIDROverlaps()

        if not existing_cidrs:
            return

        for cidr in request_cidrs:
            if self.check_cidr_in_list(cidr, existing_cidrs or []):
                raise esnat_exc.FailedToCreateElasticSnatCIDROverlaps()

    @db_base_plugin_common.convert_result_to_dict
    def create_elastic_snat(self, context, elastic_snat):
        snat = elastic_snat[api_def.RESOURCE_NAME]

        if snat.get("subnets") and snat.get("internal_cidrs"):
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Could not create Elastic Snat with "
                      "'subnets' and 'internal_cidrs' at the same time")

        internal_cidrs = snat.get("internal_cidrs", [])
        if (internal_cidrs and
                not cfg.CONF.allow_elastic_snat_cidrs_overlapping):
            rules = self.get_elastic_snats(
                context, filters={"router_id": [snat['router_id']]})
            existing_cidrs = []
            for rule in rules:
                existing_cidrs.extend(rule['internal_cidrs'])
            self.check_ip_overlaps(internal_cidrs, existing_cidrs)

        # Check esnats limit
        max_esnats = cfg.CONF.max_esnats_per_router
        if max_esnats < 0:
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Could not create Elastic Snat while "
                "max_esnats_per_router setting error.")
        if max_esnats > 0:
            num_esnats = elastic_snat_obj.ElasticSnat.count(
                context, router_id=snat['router_id'])
            if num_esnats >= max_esnats:
                raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                    error="Could not create Elastic Snat while "
                    "router meets the maximum number of esnats")

        # Check esnat rules limit
        max_esnat_rules = cfg.CONF.max_esnat_rules_per_router
        if max_esnat_rules < 0:
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Could not create Elastic Snat while "
                "max_esnat_rules_per_router setting error.")
        if max_esnat_rules > 0:
            num_esnat_rules = elastic_snat_obj.ElasticSnatRule.count(
                context, router_id=snat['router_id'])
            if snat.get("subnets"):
                num_added_esnat_rules = len(snat.get("subnets"))
            else:
                num_added_esnat_rules = len(snat.get("internal_cidrs"))
            num_esnat_rules = (num_esnat_rules + num_added_esnat_rules)
            if num_esnat_rules > max_esnat_rules:
                raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                    error="Could not create Elastic Snat while "
                    "router meets the maximum number of esnat rules")

        # Check router external gateway
        filters = {'device_id': [snat['router_id']],
                   'device_owner': [lib_consts.DEVICE_OWNER_ROUTER_GW]}
        ports = self.core_plugin.get_ports(context.elevated(), filters)
        if ports:
            router = self.l3_plugin.get_router(context, snat['router_id'])
            gw_info = router.get(l3_apidef.EXTERNAL_GW_INFO, {})
            if (not cfg.CONF.allow_activate_router_gateway_enable_snat and
                    gw_info.get('enable_snat')):
                raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                    error="Could not create Elastic Snat while "
                          "router gateway's enable_snat is True")
            snat['gateway_port_id'] = ports[0]['id']
        else:
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Could not create Elastic Snat while "
                      "router has no external gateway")

        self._check_router_infos(context, snat['router_id'],
                                 snat.get('subnets', []),
                                 snat.get('internal_cidrs', []))

        fip_id = snat.get('floatingip_id')
        fip = router_obj.FloatingIP.get_object(context, id=fip_id)

        if fip.get('fixed_port_id'):
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Could not create Elastic Snat while "
                      "this port has been associated with floatingip")

        try:
            # body 'snat' contains both tenant_id and project_id
            snat.pop("tenant_id", None)
            with db_api.context_manager.writer.using(context):
                esnat_obj = elastic_snat_obj.ElasticSnat(context,
                                                         **snat)
                esnat_obj.create()

                # Update floating IP router_id
                fip.update_fields({"router_id": snat["router_id"],
                                   "admin_state_up": True})
                fip.update()
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            if 'floatingip_id' in e.msg:
                raise esnat_exc.DuplicatedFloatingIPForElasticSnat(
                    floatingip_id=snat['floatingip_id'])
            else:
                raise esnat_exc.FailedToCreateOrUpdateElasticSnat(error=e)
        except db_exc.DBReferenceError:
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Router external gateway or floating IP does not exist "
                      "or has already been removed by concurrent operation")

        registry.publish(api_def.ELASTIC_SNAT, events.AFTER_CREATE, self,
                         payload=events.DBEventPayload(
                             context, states=(esnat_obj,),
                             resource_id=esnat_obj.id))
        self.driver.create_elastic_snat(context, esnat_obj)
        return esnat_obj

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_UPDATE])
    def _prevent_update_router_gateawy(self, resource, event,
                                       trigger, payload=None):
        context = payload.context
        router_id = payload.resource_id
        req_router = payload.request_body

        if cfg.CONF.allow_activate_router_gateway_enable_snat:
            return

        enable_snat = req_router.get(l3_apidef.ROUTER, {}).get(
            l3_apidef.EXTERNAL_GW_INFO, {}).get('enable_snat')
        if enable_snat and elastic_snat_obj.ElasticSnat.get_objects(
                context, router_id=router_id):
            raise esnat_exc.CannotChangeEnableSnatState(id=router_id)

    @registry.receives(resources.ROUTER_GATEWAY, [events.BEFORE_DELETE])
    def _prevent_delete_router_gateawy(self, resource, event,
                                       trigger, payload=None):
        if cfg.CONF.allow_remove_router_gateway_if_has_snats:
            return
        context = payload.context
        router_id = payload.resource_id
        if elastic_snat_obj.ElasticSnat.get_objects(
                context, router_id=router_id):
            raise esnat_exc.RouterGatewayInUseByElasticSnat(id=router_id)

    @registry.receives(resources.ROUTER_INTERFACE, [events.BEFORE_DELETE])
    def _prevent_delete_router_interface(self, resource, event,
                                         trigger, **kwargs):
        if cfg.CONF.allow_remove_router_interface_if_has_snats:
            return
        context = kwargs['context']
        router_id = kwargs['router_id']
        subnet_id = kwargs['subnet_id']
        subnet = self.core_plugin.get_subnet(context.elevated(), id=subnet_id)
        network = netaddr.IPNetwork(subnet['cidr'])
        elastic_snats = elastic_snat_obj.ElasticSnat.get_objects(
            context, router_id=router_id)
        for snat in elastic_snats:
            if (subnet_id in snat.subnets or any(
                    [netaddr.IPNetwork(cidr) in network for cidr in (
                        snat.internal_cidrs)])):
                raise esnat_exc.RouterInterfaceInUseByElasticSnat(
                    id=router_id)

    @registry.receives(resources.ROUTER_GATEWAY, [events.PRECOMMIT_UPDATE])
    def prevent_set_router_external_gateway_enable_snat_to_true(
            self, resource, event, trigger, payload=None):
        if cfg.CONF.allow_activate_router_gateway_enable_snat:
            return

        enable_snat = payload.request_body[l3_apidef.ROUTER].get(
            l3_apidef.EXTERNAL_GW_INFO, {}).get("enable_snat")
        if enable_snat:
            raise esnat_exc.SetRouterGatewayEnableSnatTrueNotAllowed(
                router_id=payload.resource_id)

    @registry.receives(resources.FLOATING_IP, [events.BEFORE_UPDATE])
    def _prevent_update_fip(self, resource, event, trigger, payload=None):
        context = payload.context
        fip_id = payload.resource_id
        req_fip = payload.request_body

        if not req_fip['floatingip'].get('port_id'):
            return

        elastic_snats = elastic_snat_obj.ElasticSnat.get_objects(
            context, floatingip_id=fip_id)
        if not elastic_snats:
            return

        fip_db = self.l3_plugin.get_floatingip(context, fip_id)
        for snat in elastic_snats:
            if snat.router_id == fip_db['router_id']:
                raise esnat_exc.FipInUseByElasticSnat(id=fip_id)

    @registry.receives(resources.FLOATING_IP, [events.BEFORE_DELETE])
    def _prevent_delete_fip(self, resource, event, trigger, payload=None):
        context = payload.context
        fip_id = payload.resource_id
        floatingip = payload.request_body

        if elastic_snat_obj.ElasticSnat.get_object(
                context,
                router_id=floatingip['router_id'],
                floatingip_id=fip_id):
            raise esnat_exc.FipInUseByElasticSnat(id=fip_id)

    @db_base_plugin_common.convert_result_to_dict
    def update_elastic_snat(self, context, id, elastic_snat):
        snat = elastic_snat[api_def.RESOURCE_NAME]

        if snat.get("subnets") and snat.get("internal_cidrs"):
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="cloud not create ElasticSnat with "
                      "'subnets' and 'internal_cidrs' at the same time")

        esnat_obj = self._get_elastic_snat(context, id)
        internal_cidrs = snat.get('internal_cidrs', [])

        # Check esnat rules limit
        max_esnat_rules = cfg.CONF.max_esnat_rules_per_router
        if max_esnat_rules < 0:
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Could not create Elastic Snat while "
                "max_esnat_rules_per_router setting error.")
        if max_esnat_rules > 0:
            num_esnat_rules = elastic_snat_obj.ElasticSnatRule.count(
                context, router_id=esnat_obj.router_id)
            if snat.get("subnets"):
                num_updated_rules = len(snat.get("subnets"))
            else:
                num_updated_rules = len(snat.get("internal_cidrs"))
            num_esnat_rules = (num_esnat_rules -
                            len(esnat_obj.get('internal_cidrs')) +
                            num_updated_rules)
            if num_esnat_rules > max_esnat_rules:
                raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                    error="Could not create Elastic Snat while "
                    "router meets the maximum number of esnat rules")

        if (internal_cidrs and
                not cfg.CONF.allow_elastic_snat_cidrs_overlapping):
            rules = self.get_elastic_snats(
                context, filters={"router_id": [esnat_obj.router_id]})
            existing_cidrs = []
            for rule in rules:
                if esnat_obj.id == rule['id']:
                    continue
                existing_cidrs.extend(rule['internal_cidrs'])
            self.check_ip_overlaps(internal_cidrs, existing_cidrs)

        self._check_router_infos(context, esnat_obj.router_id,
                                 snat.get('subnets', []),
                                 internal_cidrs)

        try:
            with db_api.context_manager.writer.using(context):
                esnat_obj.update_fields(snat)
                esnat_obj.update()
        except db_exc.DBReferenceError:
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(
                error="Router or floating IP does not exist or "
                      "has already been removed by concurrent operation")
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise esnat_exc.FailedToCreateOrUpdateElasticSnat(error=e)

        if 'subnets' in snat.keys() or 'internal_cidrs' in snat.keys():
            registry.publish(api_def.ELASTIC_SNAT, events.AFTER_UPDATE, self,
                             payload=events.DBEventPayload(
                                 context, states=(esnat_obj,),
                                 resource_id=esnat_obj.id))
        self.driver.update_elastic_snat(context, esnat_obj)
        return esnat_obj

    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_elastic_snat(self, context, id, fields=None):
        return self._get_elastic_snat(context, id)

    def _validate_filter_for_port_forwarding(self, request_filter):
        if not request_filter:
            return
        for filter_member_key in request_filter.keys():
            if filter_member_key in elastic_snat_obj.FIELDS_NOT_SUPPORT_FILTER:
                raise esnat_exc.ElasticSnatNotSupportFilterField(
                    filter=filter_member_key)

    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_elastic_snats(self, context, filters=None, fields=None, sorts=None,
                          limit=None, marker=None, page_reverse=False):
        filters = filters or {}
        self._validate_filter_for_port_forwarding(filters)
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        return elastic_snat_obj.ElasticSnat.get_objects(
            context, _pager=pager, **filters)

    def delete_elastic_snat(self, context, id):
        esnat_obj = self._get_elastic_snat(context, id)
        registry.publish(api_def.ELASTIC_SNAT, events.BEFORE_DELETE, self,
                         payload=events.DBEventPayload(
                             context, states=(esnat_obj,),
                             resource_id=esnat_obj.id))
        all_es_filter = [
            es_db.ElasticSnat.floatingip_id == esnat_obj.floatingip_id]
        all_es_count = context.session.query(es_db.ElasticSnat).filter(
            *all_es_filter).count()

        es_filter = [
            es_db.ElasticSnat.floatingip_id == esnat_obj.floatingip_id,
            es_db.ElasticSnat.id == id]
        es_count = context.session.query(es_db.ElasticSnat).filter(
            *es_filter).count()

        pf_filter = [
            pf_db.PortForwarding.floatingip_id == esnat_obj.floatingip_id]
        pf_count = context.session.query(pf_db.PortForwarding).filter(
            *pf_filter).count()
        with (db_api.context_manager.writer.using(context)):
            # Update floating IP router_id
            fip = router_obj.FloatingIP.get_object(context,
                                                   id=esnat_obj.floatingip_id)
            # Set the floatingip admin_state_up to False and notify its router
            # to update when there are no any DNAT resources and (no any SNAT
            # resources or the last SNAT resources to be deleted).
            if (pf_count == 0 and
                    (all_es_count == 0 or all_es_count == es_count == 1)):
                fip.update_fields({"router_id": None,
                                   "admin_state_up": False})
                fip.update()

            esnat_obj.delete()
        registry.publish(api_def.ELASTIC_SNAT, events.AFTER_DELETE, self,
                         payload=events.DBEventPayload(
                             context, states=(esnat_obj,),
                             resource_id=esnat_obj.id))
        self.driver.delete_elastic_snat(context, esnat_obj)

    def sync_elastic_snat_info(self, context, routers):
        if not routers:
            return

        for router in routers:
            rules = self.get_elastic_snats(
                context, filters={"router_id": [router['id']]})
            fip_ids = [rule['floatingip_id'] for rule in rules]
            router['fip_managed_by_elastic_snats'] = fip_ids
            fip_filters = {"id": fip_ids}
            floatingips = self.driver.l3_plugin.get_floatingips(
                context, filters=fip_filters)
            router[ELASTIC_SNAT_FLOATINGIP_KEY] = floatingips
            router['elastic_snat_fip_set'] = [
                utils.ip_to_cidr(r['floating_ip_address'], 32) for r in rules]
            router['_elastic_snat_rules'] = rules
