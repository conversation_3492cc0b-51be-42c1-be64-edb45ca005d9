# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import time

from neutron_lib.agent import topics
from neutron_lib.api.definitions import metering as metering_apidef
from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources as resource_lib
from neutron_lib import constants as const_lib
from neutron_lib import context as n_ctx
from neutron_lib.objects import exceptions as obj_exc
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory

from neutron_lib.exceptions import metering as metering_exc
from oslo_config import cfg
from oslo_log import helpers as log_helpers
from oslo_log import log as logging

from neutron.api.rpc.agentnotifiers import metering_rpc_agent_api
from neutron.api.rpc.callbacks import resources as resource_n
from neutron.common import constants
from neutron.common import exceptions
from neutron.common import rpc as n_rpc
from neutron.common import utils
from neutron.db import api as db_api
from neutron.db.metering import metering_db
from neutron.db.metering import metering_rpc
from neutron.extensions import _address_group_lib as agdef
from neutron.extensions import _elastic_snat as snat_def
from neutron.extensions import _metering_address_group_lib as metering_agdef
from neutron.objects import elastic_snat as elastic_snat_obj
from neutron.objects import metering as metering_objs
from neutron.objects import port_forwarding
from neutron.objects import router as router_obj
from neutron.objects import subnet as subnet_obj
from neutron import service

LOG = logging.getLogger(__name__)
INGRESS = 'ingress'
EGRESS = 'egress'
WASTE_TIME_THRESHOLD = 10
INGRESS_MASK = 0b1
EGRESS_MASK = 0b10
SYNC_METER_TIME_THRESHOLD = 3600
INC_STEP = 40
SNAT_DESC = 'snat_meter'


class MeteringPlugin(metering_db.MeteringDbMixin):
    """Implementation of the Neutron Metering Service Plugin."""
    supported_extension_aliases = [metering_apidef.ALIAS,
                                   metering_agdef.ALIAS]
    path_prefix = "/metering"
    __filter_validation_support = True

    def __init__(self):
        super(MeteringPlugin, self).__init__()

        self.meter_rpc = metering_rpc_agent_api.MeteringAgentNotifyAPI()
        rpc_worker = service.RpcWorker([self], worker_process_count=0)

        self.add_worker(rpc_worker)
        self.start_rpc_listeners()
        self.l3plugin = directory.get_plugin(plugin_constants.L3)
        if cfg.CONF.enable_meter_port_forwarding or not cfg.CONF.is_sdn_arch:
            registry.subscribe(self.create_portforwarding_meter,
                               resource_n.PORTFORWARDING, events.AFTER_CREATE)
            registry.subscribe(self.delete_portforwarding_meter,
                               resource_n.PORTFORWARDING, events.AFTER_DELETE)
            registry.subscribe(self.update_portforwarding_meter,
                               resource_n.PORTFORWARDING, events.AFTER_UPDATE)
        registry.subscribe(self.update_fip_meter, resource_lib.FLOATING_IP,
                           events.AFTER_UPDATE)
        registry.subscribe(self.delete_fip_meter, resource_lib.FLOATING_IP,
                           events.AFTER_DELETE)
        registry.subscribe(self.delete_fip_meter, resource_lib.PORT,
                           events.PRECOMMIT_DELETE)
        if cfg.CONF.enable_meter_snat_eip and not cfg.CONF.is_sdn_arch:
            registry.subscribe(self.create_snat_meter, snat_def.ELASTIC_SNAT,
                               events.AFTER_CREATE)
            registry.subscribe(self.update_snat_meter, snat_def.ELASTIC_SNAT,
                               events.AFTER_UPDATE)
            registry.subscribe(self.delete_snat_meter, snat_def.ELASTIC_SNAT,
                               events.AFTER_DELETE)
        registry.subscribe(self.update_address_group_meter,
                           agdef.RESOURCE_NAME, events.AFTER_UPDATE)

    def start_rpc_listeners(self):
        self.endpoints = [metering_rpc.MeteringRpcCallbacks(self)]
        self.conn = n_rpc.Connection()
        self.conn.create_consumer(
            topics.METERING_PLUGIN, self.endpoints, fanout=False)
        self.conn.create_consumer(
            topics.METERING_COLLECT_PLUGIN, self.endpoints, fanout=False)
        return self.conn.consume_in_threads()

    def create_metering_label(self, context, metering_label):
        label = metering_label['metering_label']
        action = label.get('action', 'label')

        if 'sync' == action:
            sync_data = self.create_metering_floatingip(context, label)
            sync_data = {'action': sync_data}
            return sync_data
        elif 'clean' == action:
            clean_data = self.clean_metering_floatingip(context, label)
            clean_data = {'action': clean_data}
            return clean_data
        else:
            t1 = time.time()
            LOG.info('Create_metering_label begin %s', time.time())
            ip_version = label.get('ip_version', None)
            if ip_version is None:
                ip_version = label.get('ipversion', 'ipv4')

            label['ipversion'] = ip_version
            # repeat checking
            floating_ip_id = label.get('floatingip_id')
            if floating_ip_id:
                lable_filters = {'floatingip_id': floating_ip_id}
                exist_labels = self.get_metering_labels(context, lable_filters)
                for exist_label in exist_labels:
                    meter_type = exist_label.get(
                        'type', constants.METERING_TYPE_EIP)
                    if meter_type != constants.METERING_TYPE_PF_EIP:
                        label_id = exist_label['id']
                        LOG.info("Found exist to delete exist_labels=%s",
                                 exist_labels)
                        self.delete_metering_label(context, label_id)
            label = super(MeteringPlugin, self).create_metering_label(context,
                                                                      label)
            LOG.debug('Add meter label before get full data %s', time.time())
            data = self.get_sync_full_data_metering_by_labelid(context,
                                                               label['id'])

            LOG.debug('Add meter label after get full data %s', time.time())
            if data:
                self.meter_rpc.add_metering_label(context, data)
            LOG.info('Add meter label escape: %s', time.time() - t1)
            return label

    def delete_metering_label(self, context, label_id):
        data = self.get_sync_full_data_metering_by_labelid(context, label_id,
                                                           'delete')
        label = super(MeteringPlugin, self).delete_metering_label(
            context, label_id)
        self.meter_rpc.remove_metering_label(context, data)

        return label

    def create_metering_label_rule(self, context, metering_label_rule):
        rule = super(MeteringPlugin, self).create_metering_label_rule(
            context, metering_label_rule)

        data = self.get_sync_data_for_rule(context, rule)
        self.meter_rpc.add_metering_label_rule(context, data)

        return rule

    def delete_metering_label_rule(self, context, rule_id):
        rule = super(MeteringPlugin, self).delete_metering_label_rule(
            context, rule_id)

        data = self.get_sync_data_for_rule(context, rule)
        self.meter_rpc.remove_metering_label_rule(context, data)
        return rule

    def delete_metering_router(self, context, metering_router):
        LOG.debug("delete_metering_iptable entry metering_router=%s",
                  metering_router)
        self.meter_rpc.clean_metering_router_iptable(context, metering_router)
        return

    def get_metering_router(self, context, filters=None, fields=None,
                            sorts=None, limit=None, marker=None,
                            page_reverse=False):
        return {'router_id': '', 'ip_version': ''}

    @log_helpers.log_method_call
    def create_metering_vpc(self, context, metering_vpc):
        flag = False
        metering_new = metering_vpc['metering_vpc']

        metering_vpcs = self.get_metering_vpcs(context)
        if metering_vpcs:
            try:
                for vpcs in metering_vpcs:
                    if metering_new['router_id'] == vpcs['router_id'] and \
                            metering_new['destination_ip'] == \
                            vpcs['destination_ip']\
                            and metering_new['project_id'] == \
                            vpcs['project_id']:
                        flag = True
                        break
            except Exception as compare_error:
                LOG.error('Failed to get all metering vpcs %s', compare_error)
        if not flag:
            vpc_db = super(MeteringPlugin, self).create_metering_vpc(
                context, metering_new)
            hostname_list = [i for i in vpc_db['hostname'].split(',')]
            vpc_db['hostname'] = hostname_list
            self.meter_rpc.add_metering_vpc(context, vpc_db)
            return vpc_db
        else:
            raise metering_exc.MeteringVpcConflict()

    @log_helpers.log_method_call
    def delete_metering_vpc(self, context, metering_vpc):
        metering_vpc_deleted = []
        if '+' not in metering_vpc:
            vpc = super(MeteringPlugin, self).delete_metering_vpc(
                context, metering_vpc)
            metering_vpc_deleted.append(vpc)
        else:
            vpc_list = tuple(metering_vpc.split('+'))
            router_id = vpc_list[0]
            destination_ip = vpc_list[1]
            if destination_ip != '':
                filters = {'router_id': router_id,
                           'destination_ip': destination_ip}
            else:
                filters = {'router_id': router_id}
            metering_vpcs_db = self.get_metering_vpcs(context,
                                                      filters=filters)
            if metering_vpcs_db:
                for vpc_db in metering_vpcs_db:
                    vpc = super(MeteringPlugin, self).delete_metering_vpc(
                        context, vpc_db['id'])
                    hostname_list = [i for i in vpc_db['hostname'].split(',')]
                    vpc_db['hostname'] = hostname_list
                    metering_vpc_deleted.append(vpc_db)
        if metering_vpc_deleted:
            self.meter_rpc.remove_metering_vpc(context, metering_vpc_deleted)
        return vpc

    @log_helpers.log_method_call
    def get_metering_vpc(self, context, metering_vpc, fields=None):
        with db_api.context_manager.writer.using(context):
            return super(MeteringPlugin, self).get_metering_vpc(
                context, metering_vpc, fields)

    @log_helpers.log_method_call
    def update_metering_vpc(self, context, id, metering_vpc):
        metering_vpc = metering_vpc['metering_vpc']
        vpc_db = self.update_metering_vpc_db(context, id, metering_vpc)
        hostname_list = [i for i in vpc_db['hostname'].split(',')]
        vpc_db['hostname'] = hostname_list
        self.meter_rpc.update_metering_vpc(context, vpc_db)
        return vpc_db

    def get_metering_vpcs(self, context, filters=None, fields=None,
                          sorts=None, limit=None, marker=None,
                          page_reverse=False):
        with db_api.context_manager.writer.using(context):
            return super(MeteringPlugin, self).get_metering_vpcs(
                context, filters=filters, fields=fields, sorts=sorts,
                limit=limit, marker=marker, page_reverse=page_reverse)

    def _sync_fip_meter(self, context, fip, ip_version):
        label_nums = 0
        rule_nums = 0
        router_id = fip.get('router_id', None)
        fip_addr = fip['floating_ip_address']

        filter = {'floatingip_id': fip['id']}
        labels = self.get_metering_labels(context, filters=filter)
        label = labels[0] if labels else {}
        if label and not router_id:
            self.delete_metering_label(context, label['id'])
            label_nums = label_nums - 1
            rule_nums = rule_nums - 2
            return label_nums, rule_nums, ip_version

        fip_type = fip['fip_type']
        if fip_type == const_lib.FLOATINGIP_TYPE_FIP:
            # Sync ip map floatingip meter label and rule.
            if ip_version == 'ipv4' or not ip_version:
                fixed = fip.get('fixed_ip_address', None)
                if fixed:
                    if not label:
                        label = self._check_or_create_label(
                            context, ip_version, fip,
                            constants.METERING_TYPE_EIP, 'auto')
                        label_nums = label_nums + 1
                    r_num = self._sync_meter_rule_for_label(
                        context, label, fip['fixed_ip_address'])
                    rule_nums = rule_nums + r_num
                else:
                    # Sync port forwarding floatingip meter label and rule.
                    num_l, num_r = self._sync_pf_fip(context, fip, label,
                                                     ip_version)
                    rule_nums = rule_nums + num_r
                    label_nums = label_nums + num_l
        # Sync ecs ipv6 floatingip meter label and rule.
        elif fip_type == const_lib.FLOATINGIP_TYPE_ECS_IPv6:
            if fip['admin_state_up'] and cfg.CONF.enable_meter_ecs_ipv6:
                ip_version = 'ipv6'
                fip['fixed_ip_address'] = fip_addr
                if not label:
                    label = self._check_or_create_label(
                        context, ip_version, fip,
                        constants.METERING_TYPE_ECS_EIP, 'auto')
                    label_nums = label_nums + 1
                r_num = self._sync_meter_rule_for_label(
                    context, label, fip_addr)
                rule_nums = rule_nums + r_num
        return label_nums, rule_nums, ip_version

    def _clean_multi_meter(self, context, fip_id):
        label = rule = 0
        lable_filters = {'floatingip_id': fip_id}
        exist_labels = self.get_metering_labels(context, lable_filters)
        # dirty data exist clean all
        if len(exist_labels) >= 2:
            # Clean all meters for specified fip_id.
            LOG.warning("Found same floating ip but multi labels!")
            for label in exist_labels:
                self.delete_metering_label(context, label.id)
                label = label - 1
                rule = rule - 2
        return label, rule

    def _check_or_create_label(self, context, ip_ver, fip, type, triggered):
        filter = {'floatingip_id': fip['id']}
        labels = self.get_metering_labels(context, filters=filter)
        label = labels[0] if labels else {}
        if not label:
            meter_label = self._build_metering_label_dict(
                fip['id'], fip['floating_ip_address'], fip['fixed_ip_address'],
                fip['project_id'], ip_ver, fip['router_id'], type, triggered)
            label = self._create_label(context, meter_label)
        return label

    def _sync_meter_rule_for_label(self, context, label, fixed):
        rule_num = 0
        # field = ['id', 'direction', 'remote_ip_prefix']
        for direction in const_lib.VALID_DIRECTIONS:
            filter_direction = {'metering_label_id': label['id'],
                                'direction': direction}
            exist_rules = self.get_metering_label_rules(
                context, filters=filter_direction)
            if not exist_rules:
                self._create_rule(context, label, direction=direction)
                rule_num = rule_num + 1
            else:
                map_rule = False
                for rule in exist_rules:
                    if ((utils.AuthenticIPNetwork(fixed)) ==
                            rule['remote_ip_prefix']):
                        map_rule = True
                        break
                if not map_rule:
                    self._create_rule(context, label, direction=direction)
                    rule_num = rule_num + 1
        return rule_num

    def _sync_pf_fip(self, context, fip, label, ip_version):
        refresh_vm_ip = ''
        label_nums = rule_nums = 0
        pf_objs = port_forwarding.PortForwarding.get_objects(
            context, floatingip_id=fip['id'])
        if not label:
            fip['fixed_ip_address'] = ''
            label = self._check_or_create_label(
                context, ip_version, fip,
                constants.METERING_TYPE_PF_EIP, 'auto')
            label_nums = label_nums + 1
        exist_rules = self.get_metering_label_rules(
            context, filters={'metering_label_id': [label['id']]})

        rule_num, refresh_vm_ip = self._refresh_pf_meter_rules(
            context, label, exist_rules, pf_objs, ip_version)
        rule_nums = rule_nums + rule_num
        return label_nums, rule_nums

    def _clean_snat_meter(self, context, fip_id):
        label_num = rule_num = 0
        lable_filters = {'floatingip_id': fip_id}
        exist_labels = self.get_metering_labels(context, lable_filters)
        for label in exist_labels:
            label_id = label.get('id', None)
            if label_id:
                rule_filter = {'metering_label_id': label['id']}
                rules = self.get_metering_label_rules(context,
                                                      filters=rule_filter)
                self.delete_metering_label(context, label_id)
                label_num = label_num - 1
                rule_num = rule_num - len(rules)
        return label_num, rule_num

    def _sync_snat_meter(self, context, fip, e_snat):
        label_nums = 0
        rule_nums = 0
        filter = {'floatingip_id': fip['id']}
        labels = self.get_metering_labels(context, filters=filter)
        label = labels[0] if labels else {}
        if label and not fip['router_id']:
            remove_filter = {'metering_label_id': label['id']}
            remove_rules = self.get_metering_label_rules(context,
                                                        filters=remove_filter)
            self.delete_metering_label(context, label['id'])
            label_nums = label_nums - 1
            rule_nums = rule_nums - len(remove_rules)
            return label_nums, rule_nums
        if not labels:
            # Add meter label for elastic snat
            label = self._create_snat_meter_label(context, e_snat)
            label_nums = label_nums + 1
        else:
            pf_objs = port_forwarding.PortForwarding.get_objects(
                context, floatingip_id=fip['id'])
            if pf_objs:
                label['type'] = constants.METERING_TYPE_MULTI_EIP
                self._update_metering_label_type(context, label['id'], label)
        rule_filter = {'metering_label_id': label['id']}
        rules = self.get_metering_label_rules(context, filters=rule_filter)
        is_need_add_rules = True
        for rule in rules:
            if rule.get('remote_ip_prefix') and ';' not in rule.get(
                    'remote_ip_prefix'):
                is_need_add_rules = False

        if is_need_add_rules:
            self._create_snat_meter_rules(context, e_snat, label['id'])
            rule_nums = rule_nums + len(rules)
        return label_nums, rule_nums

    def sync_floatingip_for_meter(self, context, filters):
        fip_id = filters['floatingip_id']
        ip_version = filters['ip_version']
        router_id = filters['router_id']

        add_labels = add_rules = 0
        context = n_ctx.get_admin_context()
        # Recording current meter data.
        old_meter_labels = self.get_metering_labels(context)
        old_meter_rules = self.get_metering_label_rules(context)
        current_labels = len(old_meter_labels)
        current_labels_rule = len(old_meter_rules)
        # Get floating ips according to filters.
        floatingips = []
        filter_fip = {}

        if fip_id:
            # Sync specify floating ip meter label and rules.
            filter_fip = {'id': fip_id}
        elif router_id:
            # Sync all floating ips according to ip_version and router.
            filter_fip = {'router_id': router_id}
        else:
            # Sync all floatingips according to ip_version.
            if ip_version == 'ipv6' and cfg.CONF.enable_meter_ecs_ipv6:
                filter_fip = {'fip_type': const_lib.FLOATINGIP_TYPE_ECS_IPv6}
            else:
                ip_version = 'ipv4'
                filter_fip = {'fip_type': const_lib.FLOATINGIP_TYPE_FIP}
        floatingips = self.l3plugin.get_floatingips(context, filter_fip)

        snat_fips = set()
        if cfg.CONF.enable_meter_snat_eip and not cfg.CONF.is_sdn_arch:
            elastic_rules = elastic_snat_obj.ElasticSnat.get_objects(context)
            for e_snat in elastic_rules:
                snat_fips.add(e_snat['floatingip_id'])

        sync_data = {}
        # Thousands of fip cost may hours, force break
        t1 = time.time()
        inc = 1
        for fip in floatingips:
            # Clean up dirty data.
            if time.time() - t1 >= SYNC_METER_TIME_THRESHOLD:
                LOG.warning("sync meter loop hold too long time")
                break
            if inc % INC_STEP == 0:
                break

            if not fip['router_id']:
                continue
            if fip['id'] in snat_fips:
                pf_objs = port_forwarding.PortForwarding.get_objects(
                    context, floatingip_id=fip['id'])
                if not pf_objs:
                    continue
            fip_ip_version = self.get_version_for_ip_address(
                fip['floating_ip_address'])
            if ip_version and ip_version != fip_ip_version and not fip_id:
                continue
            else:
                del_l, del_r = self._clean_multi_meter(context, fip['id'])
                add_l, add_r, ip_version = self._sync_fip_meter(context, fip,
                                                                ip_version)
            if add_l != 0 or add_r != 0:
                inc = inc + 1
            current_labels = current_labels + del_l + add_l
            current_labels_rule = current_labels_rule + del_r + add_r
            add_labels = add_labels + add_l
            add_rules = add_rules + add_r

        if cfg.CONF.enable_meter_snat_eip:
            for e_snat in elastic_rules:
                fip = self.l3plugin.get_floatingip(context,
                                                   e_snat['floatingip_id'])
                add_l, add_r = self._sync_snat_meter(context, fip, e_snat)
                current_labels = current_labels + add_l
                current_labels_rule = current_labels_rule + add_r
                add_labels = add_labels + add_l
                add_rules = add_rules + add_r
        sync_data = {'before_sync_labels': len(old_meter_labels),
                     'before_sync_label_rules': len(old_meter_rules),
                     'after_sync_labels': current_labels,
                     'after_sync_label_rules': current_labels_rule,
                     'labels_added': add_labels,
                     'label_rules_added': add_rules,
                     'ip_version': ip_version}
        return sync_data

    def create_metering_floatingip(self, context, label):
        filters = {}
        if cfg.CONF.is_sdn_arch:
            return
        ip_version = label.get('ip_version')
        floatingip_id = label.get('floatingip_id', None)
        router_id = label.get('router_id', None)
        filters = {'ip_version': ip_version, 'floatingip_id': floatingip_id,
                   'router_id': router_id}
        sync_data = {}
        sync_data = self.sync_floatingip_for_meter(context, filters)
        return sync_data

    def clean_metering_floatingip(self, context, metering_label):
        if cfg.CONF.is_sdn_arch:
            return
        inc = 0
        inc_err = 0
        INC_STEP = 40
        INC_MAX_ERR = 3
        ip_version = metering_label.get('ip_version', 'ipv4')
        router_id = metering_label.get('router_id', None)
        floatingip_id = metering_label.get('floatingip_id', None)
        triggered = metering_label['triggeredtype']

        before_clean_labels = len(self.get_metering_labels(context))
        before_clean_label_rules = len(self.get_metering_label_rules(context))

        filter_m = {'triggeredtype': triggered}
        if floatingip_id:
            filter_m.update({'floatingip_id': floatingip_id})
        elif router_id:
            filter_m.update({'router_id': router_id})
        else:
            filter_m.update({'ipversion': ip_version})
        metering_labels = self.get_metering_labels(context, filters=filter_m)
        if metering_labels:
            for metering_label in metering_labels:
                inc += 1
                if inc % INC_STEP == 0:
                    break
                context = n_ctx.get_admin_context()
                try:
                    meter_id = metering_label['id']
                    self.delete_metering_label(context, meter_id)
                except Exception as e:
                    LOG.error('Clean metering floating ip failed'
                              ' %s metering_label=%s', e, metering_label)
                    if inc_err >= INC_MAX_ERR:
                        # in case too much error log
                        LOG.error('Clean metering floating ip failed '
                                  'over %s times', INC_MAX_ERR)
                        break
                    inc_err += 1

        after_clean_labels = len(self.get_metering_labels(context))
        after_clean_label_rules = len(self.get_metering_label_rules(context))
        clean_date = {'before_clean_labels': before_clean_labels,
                      'before_clean_label_rules': before_clean_label_rules,
                      'after_clean_labels': after_clean_labels,
                      'after_clean_label_rules': after_clean_label_rules}
        return clean_date

    def router_deleted(self, context, router_id):
        filters = {'router_id': router_id}
        labels = self.get_metering_labels(context, filters=filters)
        if labels:
            for label in labels:
                super(MeteringPlugin, self).delete_metering_label(context,
                                                                  label['id'])
        LOG.debug("\n router=%s deleted, lables=%s binded "
                  "with router was removed.", router_id, labels)
        self.meter_rpc.router_deleted(context, router_id)

        vpc_db = self.get_metering_vpcs(context, filters=filters)
        if vpc_db:
            for vpc in vpc_db:
                self.delete_metering_vpc(context, vpc['id'])

    def sync_metering_vpc_routers(self, context, host, sync_vpcs):
        if sync_vpcs:
            for meter_vpc in sync_vpcs:
                try:
                    router = super(MeteringPlugin, self).\
                        sync_metering_vpc_routers(context, meter_vpc)
                    LOG.debug('MeteringAgent:sync vpc router: %s', router)
                except metering_exc.MeteringVpcSyncFailed:
                    raise metering_exc.MeteringVpcSyncFailed()
                except Exception:
                    LOG.error('Failed to get router from db when sync '
                              'metering vpc')
                    continue
                if not router:
                    LOG.debug('Router %s is not exist ,so delete '
                              'metering vpc %s', meter_vpc['router_id'],
                              meter_vpc['id'])
                    try:
                        vpc_db = self.get_metering_vpc(
                            context, meter_vpc['id'])
                    except metering_exc.MeteringVpcNotFound:
                        LOG.debug('metering vpc %s has been delete %s',
                                  meter_vpc['id'])
                        continue
                    self.delete_metering_vpc(context, vpc_db['id'])
                else:
                    LOG.warning('Update L3 agents hosting a router')
                    vpc_db = self.get_metering_vpc(context, meter_vpc['id'])
                    self.update_metering_vpc_db(context, meter_vpc['id'],
                                                vpc_db)

    @staticmethod
    def _build_metering_label_dict(floatingip_id, floatingip, remote_ip_prefix,
                                   tenant_id, ipversion, router_id, type,
                                   triggeredtype='service'):
        return {'metering_label': {
            'description': remote_ip_prefix,
            'name': floatingip,
            'tenant_id': tenant_id,
            'shared': False,
            'floatingip_id': floatingip_id,
            'triggeredtype': triggeredtype,
            'ipversion': ipversion,
            'router_id': router_id,
            'type': type
        }}

    @staticmethod
    def _build_metering_label_rule_dict(remote_ip_prefix,
                                        direction,
                                        metering_label_id,
                                        rule_type,
                                        excluded=False):
        return {'metering_label_rule': {
            'remote_ip_prefix': remote_ip_prefix, 'direction': direction,
            'metering_label_id': metering_label_id, 'excluded': excluded,
            'type': rule_type
        }}

    def _create_rule(self, context, meter_label,
                     type=constants.METERING_TYPE_PF_EIP, direction=None):
        if not meter_label:
            return

        label_id = meter_label['id']
        begin_time = time.time()
        LOG.debug("Create rule by meter_label=%s", meter_label)
        rule_direction = dict.fromkeys([const_lib.INGRESS_DIRECTION,
                                        const_lib.EGRESS_DIRECTION])
        if direction in const_lib.VALID_DIRECTIONS:
            label_rule_direction = self._build_metering_label_rule_dict(
                meter_label['description'], direction, label_id, type, False)
            rule_direction[direction] = self.create_metering_label_rule(
                context, label_rule_direction)
        else:
            for dire in const_lib.VALID_DIRECTIONS:
                label_rule_direction = self._build_metering_label_rule_dict(
                    meter_label['description'], dire, label_id, type, False)
                rule_direction[dire] = self.create_metering_label_rule(
                    context, label_rule_direction)
        escaped_time = time.time() - begin_time
        if escaped_time > WASTE_TIME_THRESHOLD:
            LOG.warning('Create meter rule escaped %s', escaped_time)

        return rule_direction['ingress'], rule_direction['egress']

    def _create_label(self, context, meter_label):
        ret_label = {}
        begin_time = time.time()
        ret_label = self.create_metering_label(context, meter_label)
        escaped_time = time.time() - begin_time
        if escaped_time > WASTE_TIME_THRESHOLD:
            LOG.warning('Create meter lable escaped %s', escaped_time)

        return ret_label

    @staticmethod
    def _get_rule_key(ip, direction):
        return '%s_%s' % (ip, direction)

    def _update_metering_label_type(self, context, label_id, label):
        try:
            with db_api.context_manager.writer.using(context):
                label_obj = metering_objs.MeteringLabel.get_object(
                    context=context, id=label_id)
                label_obj.update_fields(label)
                label_obj.update()
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise exceptions.FailedToUpdateMeteringLable(error=e)

    def create_portforwarding_meter(
            self, resource, event, trigger, context, **kwargs):
        """Create metering for port forwarding.
        @param pf_obj:  old pf obj
        @param project_id: tenant id
        @return:  none
        """
        pf_obj = kwargs['pf_obj']
        tenant_id = kwargs['project_id']
        ip_version = self.get_version_for_ip_address(str(
            pf_obj.floating_ip_address))

        if not all(tenant_id and pf_obj):
            LOG.error('Metering need tenant_id and floatingip for '
                      'portforwarding createing')
            return

        if str(pf_obj.floating_ip_address):
            if ip_version != 'ipv4':
                return

        label_db = {}
        ctx = n_ctx.get_admin_context()
        lable_filters = {'floatingip_id': pf_obj.floatingip_id,
                         'project_id': tenant_id}
        exist_label = self.get_metering_labels(ctx, lable_filters)
        desciption = str(pf_obj.internal_ip_address) + ';' + str(
            pf_obj.protocol) + ';' + str(pf_obj.internal_port)
        if not exist_label:
            meter_label = self._build_metering_label_dict(
                pf_obj.floatingip_id, str(pf_obj.floating_ip_address),
                str(desciption), tenant_id, ip_version,
                pf_obj.router_id, constants.METERING_TYPE_PF_EIP, 'service')

            label_db = self._create_label(ctx, meter_label)
            LOG.debug("First portforward metering label: %s", label_db)
            self._create_rule(ctx, label_db)

        else:
            label_db = exist_label[0]
            meter_db_type = label_db['type']
            pf_cidr = str(desciption)
            if meter_db_type in (constants.METERING_TYPE_EIP,
                                 constants.METERING_TYPE_ECS_EIP):
                LOG.error('Floatingip %s label type is not pf_eip',
                          pf_obj.floatingip_id)
                return
            elif meter_db_type in (constants.METERING_TYPE_PF_EIP,
                                   constants.METERING_TYPE_MULTI_EIP):
                self._update_pf_label_rules(ctx, label_db, pf_obj)
            elif meter_db_type == constants.METERING_TYPE_SNAT_EIP:
                # Update metering label type from snat_eip to multiple_eip
                label_db['type'] = constants.METERING_TYPE_MULTI_EIP
                label_db['description'] = pf_cidr
                self._update_metering_label_type(ctx, label_db['id'], label_db)
                self._create_rule(ctx, label_db)

    def _update_pf_label_rules(self, context, label_db, pf_obj):
        # Reuse lable, but need fill the new inerternal ip address
        ctx = n_ctx.get_admin_context()
        new_rule_cidr = str(pf_obj.internal_ip_address) + ';' + str(
            pf_obj.protocol) + ';' + str(pf_obj.internal_port)
        label_db['description'] = str(new_rule_cidr)
        LOG.debug("Repeate portforward metering label: %s", label_db)

        filter = {'metering_label_id': label_db['id'],
                  'type': constants.METERING_TYPE_PF_EIP}
        rules = self.get_metering_label_rules(ctx, filters=filter)
        vm_ip_exist_meter_set = set()
        vm_rule_ids = {}
        for r in rules:
            vm_ip = str(r['remote_ip_prefix'])
            vm_ip_exist_meter_set.add(vm_ip)
            r_key = self._get_rule_key(vm_ip, r['direction'])
            vm_rule_ids[r_key] = r['id']

        LOG.debug("Vm ip set=%s,vm_rule_ids=%s",
                  vm_ip_exist_meter_set, vm_rule_ids)

        #  1.add rule
        if str(new_rule_cidr) not in vm_ip_exist_meter_set:
            # if not exist then add
            LOG.debug("vm ip to add")
            self._create_rule(ctx, label_db)

        #  2.dirty data to remove
        fip_pf_objs = port_forwarding.PortForwarding.get_objects(
            ctx, floatingip_id=pf_obj.floatingip_id)

        vm_ip_expect_set = set()
        for fip_obj in fip_pf_objs:
            pf_ip = str(fip_obj.internal_ip_address) + ';' + str(
                fip_obj.protocol) + ';' + str(fip_obj.internal_port)
            vm_ip_expect_set.add(str(pf_ip))
            LOG.debug("vm_ip_expect_set=%s", vm_ip_expect_set)
        to_remove_set = vm_ip_exist_meter_set - vm_ip_expect_set
        for vm in to_remove_set:
            rule_id_ingress = vm_rule_ids.get(
                self._get_rule_key(vm, 'ingress'))
            rule_id_egress = vm_rule_ids.get(
                self._get_rule_key(vm, 'egress'))
            LOG.debug("rule_id_ingress=%s rule_id_egress=%s",
                      rule_id_ingress, rule_id_egress)
            if rule_id_egress:
                self.delete_metering_label_rule(context,
                                                rule_id_egress)
            if rule_id_ingress:
                self.delete_metering_label_rule(context,
                                                rule_id_ingress)

    def _delete_portforwarding_meter(self, context, floatingip_id, tenant_id):
        label_id = self.get_label_uuid_by_floatingipid(
            context, floatingip_id, tenant_id)
        self._delete_portforwarding_meter_by_label_id(context, label_id)

    def _delete_portforwarding_meter_by_label_id(self, context, label_id):
        try:
            if label_id is None:
                LOG.warning("Delete portforwarding meter but label_id is None")
                return

            self.delete_metering_label(context, label_id)
            LOG.debug('Meter label=%s deleted for pf', label_id)

        except Exception as e:
            LOG.error('Delete pf metering failed, e:%s', e)

    def del_pf_rule(self, context, label, pf_objs, pf_obj):
        vm_ip_exist_pf_set = set()
        for pf_obj_db in pf_objs:
            vm_ip = str(pf_obj_db.internal_ip_address) + ';' + str(
                pf_obj_db.protocol) + ';' + str(pf_obj_db.internal_port)
            vm_ip_exist_pf_set.add(vm_ip)
            LOG.debug("Exist pf set=%s", vm_ip_exist_pf_set)

        delete_pf_ip = str(pf_obj.internal_ip_address) + ';' + str(
            pf_obj.protocol) + ';' + str(pf_obj.internal_port)
        if str(delete_pf_ip) not in vm_ip_exist_pf_set:
            # only delete one of the mapping vm for the floating ip
            LOG.debug("Deleting %s rules", delete_pf_ip)
            self._find_label_rule_and_remove(
                context, label['id'], str(delete_pf_ip))

    def delete_portforwarding_meter(
            self, resource, event, trigger, context, **kwargs):
        """Create metering for port forwarding.
        @param pf_obj:  old pf obj
        @param project_id: tenant id
        @return:  none
        """
        pf_obj = kwargs['pf_obj']
        tenant_id = kwargs['project_id']

        if not all([tenant_id, pf_obj]):
            LOG.error('Metering need tenant_id and floatingip for '
                      'portforwarding deleting')
            return
        fip_address = str(pf_obj.floating_ip_address)
        if fip_address:
            if self.get_version_for_ip_address(fip_address) == 'ipv6':
                return
        ctx = n_ctx.get_admin_context()
        fip_id = pf_obj['floatingip_id']

        lable_filters = {'floatingip_id': pf_obj.floatingip_id,
                         'project_id': tenant_id}
        exist_labels = self.get_metering_labels(ctx, lable_filters)

        for exist_label in exist_labels:
            type = exist_label.get('type', constants.METERING_TYPE_PF_EIP)
            LOG.debug("Deleted pf meter label exist_label=%s", exist_label)
            if type in (constants.METERING_TYPE_EIP,
                        constants.METERING_TYPE_ECS_EIP,
                        constants.METERING_TYPE_SNAT_EIP):
                LOG.warning('May l3 created label already.')
                continue
            elif type == constants.METERING_TYPE_PF_EIP:
                pf_objs = port_forwarding.PortForwarding.get_objects(
                    ctx, floatingip_id=fip_id)
                # The last pf of the floating ip, delete label and rule
                if not pf_objs:
                    self._delete_portforwarding_meter(ctx, fip_id, tenant_id)
                    return
                else:
                    self.del_pf_rule(ctx, exist_label, pf_objs, pf_obj)
            elif type == constants.METERING_TYPE_MULTI_EIP:
                pf_objs = port_forwarding.PortForwarding.get_objects(
                    ctx, floatingip_id=fip_id)
                if not pf_objs:
                    exist_label['type'] = constants.METERING_TYPE_SNAT_EIP
                    self._update_metering_label_type(
                        context, exist_label['id'], exist_label)
                self.del_pf_rule(ctx, exist_label, pf_objs, pf_obj)
                filter = {'metering_label_id': exist_label['id']}
                rules = self.get_metering_label_rules(ctx, filters=filter)
                if not rules:
                    self.delete_metering_label(ctx, exist_label['id'])

    def _find_label_rule_and_remove(self, context, label_id, ip_to_remove):
        remove_num = 0
        r_ips = self.get_metering_label_rules(
            context, filters={'metering_label_id': [label_id]},
            fields=['id', 'remote_ip_prefix'])
        for r in r_ips:
            if str(ip_to_remove) == r['remote_ip_prefix']:
                self.delete_metering_label_rule(context, r['id'])
                remove_num += 1
        return remove_num

    def _refresh_pf_meter_rules(self, context, new_meter_label, exist_rules,
                                fip_pf_objs, ip_ver='ipv4'):
        labels_rule_v4 = 0

        vm_ip_exist_meter_set = set()
        vm_rule_ids = {}
        vm_rule_key_exist_set = set()
        vm_ip = ''
        for r in exist_rules:
            vm_ip = str(r['remote_ip_prefix'])
            vm_ip_exist_meter_set.add(vm_ip)
            r_key = self._get_rule_key(vm_ip, r['direction'])
            vm_rule_ids[r_key] = r['id']
            vm_rule_key_exist_set.add(r_key)

        vm_ip_expect_set = set()
        vm_rule_key_expect_set = set()
        for fip_obj in fip_pf_objs:
            vm_ip = str(fip_obj.internal_ip_address) + ';' + str(
                fip_obj.protocol) + ';' + str(fip_obj.internal_port)
            vm_ip_expect_set.add(vm_ip)
            r_key_ingress = self._get_rule_key(vm_ip, 'ingress')
            r_key_egress = self._get_rule_key(vm_ip, 'egress')
            vm_rule_key_expect_set.add(r_key_ingress)
            vm_rule_key_expect_set.add(r_key_egress)
        to_add_rules = (vm_rule_key_expect_set - vm_rule_key_exist_set)
        LOG.debug("To_add rule=%s", to_add_rules)
        for rkey in to_add_rules:
            rkey_list = rkey.split('_')
            vm_ip = rkey_list[0]
            direction = rkey_list[1]
            new_meter_label['description'] = vm_ip
            self._create_rule(context, new_meter_label, direction=direction)
            if ip_ver == 'ipv4':
                labels_rule_v4 += 1

        to_remove_rule = (vm_rule_key_exist_set - vm_rule_key_expect_set)
        LOG.debug("To remove rule=%s", to_remove_rule)
        for rkey in to_remove_rule:
            rule_id = vm_rule_ids.get(rkey)
            if rule_id:
                LOG.debug("Delete rule_id=%s", rule_id)
                self.delete_metering_label_rule(context, rule_id)
                if ip_ver == 'ipv4':
                    labels_rule_v4 -= 1
        return labels_rule_v4, vm_ip

    def update_portforwarding_meter(
            self, resource, event, trigger, context, **kwargs):
        """Update metering for new internal ip/port/protocol/port num
        @param pf_obj:  old pf obj
        @param is_need_change_meter_ip: must ensure vm port consisted with ip
        @param the_lastest_ip_address: new ip
         @param the_lastest_internal_port:  new port
        @return:  none
        """
        # pf obj which was before updated
        old_pf_obj = kwargs['pf_obj']
        is_need_change_meter_ip = True
        the_latest_ip_address = kwargs['the_latest_ip_address']
        latest_internal_port = kwargs['the_larest_internal_port']
        latest_protocol = kwargs['the_latest_protocol']
        description = str(the_latest_ip_address) + ';' + str(
            latest_protocol) + ';' + str(latest_internal_port)
        tenant_id = kwargs['project_id']
        fip_address = str(old_pf_obj.floating_ip_address)
        ip_version = self.get_version_for_ip_address(fip_address)
        fip_id = old_pf_obj['floatingip_id']
        if fip_address:
            if ip_version != 'ipv4':
                return

        ctx = n_ctx.get_admin_context()
        if is_need_change_meter_ip:
            new_meter_label = {}

            LOG.debug("fip_id=%s tenant_id=%s", fip_id, tenant_id)
            filters = {'floatingip_id': fip_id, 'project_id': tenant_id}
            labels = metering_objs.MeteringLabel.get_objects(
                ctx, **filters)

            if labels:
                # First thinking reuse the label
                new_meter_label = labels[0]
                # check exist rules
                label_type = new_meter_label['type']
                filter = {}
                if label_type == constants.METERING_TYPE_MULTI_EIP:
                    filter = {'metering_label_id': new_meter_label['id'],
                              'type': constants.METERING_TYPE_PF_EIP}
                else:
                    filter = {'metering_label_id': [new_meter_label['id']]}
                exist_rules = self.get_metering_label_rules(
                    ctx, filters=filter,
                    fields=['id', 'direction', 'remote_ip_prefix'])

                fip_pf_objs = port_forwarding.PortForwarding.get_objects(
                    ctx, floatingip_id=fip_id)

                self._refresh_pf_meter_rules(
                    ctx, new_meter_label, exist_rules, fip_pf_objs)
                return

            # create meter label and rule
            new_meter_label = self._build_metering_label_dict(
                fip_id, fip_address, description, tenant_id,
                ip_version, old_pf_obj.router_id,
                constants.METERING_TYPE_PF_EIP, 'service')

            label_db = self._create_label(ctx, new_meter_label)
            ingress_rule, egress_rule = self._create_rule(ctx, label_db)
            LOG.debug("Update pf for meter success! "
                      "label_db=%s ingress_rule=%s, "
                      "egress_rule=%s", label_db, ingress_rule, egress_rule)

    def delete_fip_meter(self, resource, event, trigger, **kwargs):
        floatingip = {}
        filter_fip = {}
        if cfg.CONF.is_sdn_arch:
            return
        context = n_ctx.get_admin_context()
        if resource == resource_lib.PORT:
            port_info = kwargs.get('port')
            filter_fip = {'floating_port_id': port_info['id']}
        else:
            filter_fip = {'id': kwargs.get('id', '')}
        fips = self.l3plugin.get_floatingips(context, filters=filter_fip)
        if fips:
            floatingip = fips[0]
        if floatingip:
            fip_type = floatingip['fip_type']
            if fip_type == const_lib.FLOATINGIP_TYPE_ECS_IPv6:
                if not cfg.CONF.enable_meter_ecs_ipv6:
                    return
            else:
                if floatingip['fixed_ip_address']:
                    if not cfg.CONF.enable_meter_full_eip:
                        return
                else:
                    if not cfg.CONF.enable_meter_port_forwarding:
                        return
            self._delete_fip_meter(context, floatingip['id'])

    def _delete_fip_meter(self, context, fip_id):
        filters = {'floatingip_id': fip_id}
        labels = self.get_metering_labels(context, filters=filters)
        for label in labels:
            label_id = label['id']
            self.delete_metering_label(context, label_id)

    def update_fip_meter(self, resource, event, trigger, context, **kwargs):
        # Init args
        if cfg.CONF.is_sdn_arch:
            return
        fip_type = kwargs.get('fip_type')
        router_id = kwargs.get('router_id')
        last_known_router_id = kwargs.get('last_known_router_id')
        floating_ip_id = kwargs.get('floating_ip_id')
        admin_state_up = kwargs.get('admin_state_up')
        fixed_port_id = kwargs.get('fixed_port_id')
        fixed_ip_address = kwargs.get('fixed_ip_address')
        floating_ip_address = kwargs.get('floating_ip_address')
        context = n_ctx.get_admin_context()
        fip_db = self.l3plugin.get_floatingip(context, floating_ip_id)

        # Add or delete label and rules base on fip_type.
        if fip_type == const_lib.FLOATINGIP_TYPE_FIP:
            if not cfg.CONF.enable_meter_full_eip:
                return
            if router_id and fixed_ip_address and fixed_port_id:
                ipversion = self.get_version_for_ip_address(
                    floating_ip_address)
                if ipversion == 'ipv6':
                    return
                # Associate fip with fixed_port.
                label_dict = self._build_metering_label_dict(
                    floating_ip_id, floating_ip_address, fixed_ip_address,
                    fip_db['project_id'], ipversion, router_id,
                    constants.METERING_TYPE_EIP, 'service')
                label_db = self._create_label(context, label_dict)
                ingress, egress = self._create_rule(
                    context, label_db, type=constants.METERING_TYPE_EIP)
                LOG.info('Create meter rule ingress %s egress %s', ingress,
                         egress)
                LOG.info('Add meter for type of floatingip fip %s',
                         floating_ip_id)
            elif (not router_id and last_known_router_id and
                  not fixed_port_id and not fixed_ip_address):
                # Unassociate fip with fixed_port.
                self._delete_fip_meter(context, floating_ip_id)
        elif fip_type == const_lib.FLOATINGIP_TYPE_ECS_IPv6:
            if not cfg.CONF.enable_meter_ecs_ipv6:
                return
            ipversion = self.get_version_for_ip_address(floating_ip_address)
            if ipversion == 'ipv4':
                return
            # Set admin_state up.
            if router_id and admin_state_up:
                label_dict = self._build_metering_label_dict(
                    floating_ip_id, floating_ip_address, floating_ip_address,
                    fip_db['project_id'], ipversion, router_id,
                    constants.METERING_TYPE_ECS_EIP, 'service')
                label_db = self._create_label(context, label_dict)
                ingress, egress = self._create_rule(
                    context, label_db, type=constants.METERING_TYPE_ECS_EIP)
                LOG.info('Create meter rule ingress %s egress %s', ingress,
                         egress)
                LOG.info('Add meter for ECS_IPV6 fip %s', floating_ip_id)
            # Set admin_state down.
            elif not router_id or not admin_state_up:
                self._delete_fip_meter(context, floating_ip_id)

    def update_address_group_meter(self, resource, event, trigger,
                                   context, **kwargs):
        address_group = kwargs.get('address_group')
        filters = {'address_group_id': address_group['id']}
        rules = self.get_metering_label_rules(context, filters)
        for rule in rules:
            data = self.get_sync_data_for_rule(context, rule)
            self.meter_rpc.update_metering_label_rules(context, data)

    def _get_esnat_obj(self, kwargs):
        payload = kwargs.get('payload', None)
        if payload:
            esnat_obj = payload.states[0]
            context = payload.context
            return esnat_obj, context
        else:
            return None, None

    def _sort_esnat_cidr(self, context, cidrs, subnets):
        if not cidrs:
            for subnet in subnets:
                sub = subnet_obj.Subnet.get_object(context, id=subnet)
                cidrs.append(str(sub.cidr))
        return set(sorted(cidrs, key=lambda x: x[-2:], reverse=True))

    def _create_snat_meter_label(self, context, esnat_obj):
        # Parameter initialization
        router_id = esnat_obj.router_id
        fip_addr = esnat_obj.floating_ip_address
        floatingip_id = esnat_obj.floatingip_id
        fip_obj = router_obj.FloatingIP.get_object(context, id=floatingip_id)
        ipversion = self.get_version_for_ip_address(
            esnat_obj.floating_ip_address)
        # Constructing the metering_label structure
        meter_label = self._build_metering_label_dict(
            floatingip_id, str(fip_addr), SNAT_DESC, fip_obj['project_id'],
            ipversion, router_id, constants.METERING_TYPE_SNAT_EIP, 'service')
        # Create metering_label
        return self._create_label(context, meter_label)

    def _create_snat_meter_rules(self, context, esnat_obj, label_id,
                                 cidrs=set()):
        if not cidrs:
            cidrs = self._get_snat_cidrs(context, esnat_obj)
        for cidr in cidrs:
            for direction in const_lib.VALID_DIRECTIONS:
                label_rule = self._build_metering_label_rule_dict(
                    cidr, direction, label_id,
                    constants.METERING_TYPE_SNAT_EIP, False)
                # Create metering_rule
                self.create_metering_label_rule(context, label_rule)

    def _create_snat_meter_rules_async(self, context, esnat_obj, label_id,
                                       cidrs=()):
        """Process to create meter label rules quickly

        1.insert all meter label rules by label_id in db
        2.send routers_update rpc to notify metering_agent update rules
        """
        LOG.debug("Create metering label rules, label:%s.", label_id)
        if not esnat_obj or not label_id:
            return
        cidrs = cidrs if cidrs else self._get_snat_cidrs(context, esnat_obj)
        cidrs = sorted(cidrs, key=lambda x: x[-2:])
        for cidr in cidrs:
            for direction in const_lib.VALID_DIRECTIONS:
                rule = self._build_metering_label_rule_dict(
                    cidr, direction, label_id,
                    constants.METERING_TYPE_SNAT_EIP, False)
                super(MeteringPlugin, self).create_metering_label_rule(
                    context, rule)

        # Notify metering agent to pull metring rules passively
        router = {'router_id': esnat_obj.router_id,
                  'floatingip_id': esnat_obj.floatingip_id,
                  'label_id': label_id,
                  'action': 'update',
                  'label_type': constants.METERING_TYPE_SNAT_EIP}
        self.meter_rpc.update_metering(context, router)

    def sync_router_metering(self, context, router):
        LOG.info("Sync router metering router:%s.", router)
        router_id = router['router_id']
        floatingip_id = router['floatingip_id']
        label_id = router['label_id']
        label_type = router['label_type']

        # Sync elastic snat metering label rules
        if label_type.startswith('snat'):
            esnat_objects = elastic_snat_obj.ElasticSnat.get_objects(
                context, router_id=router_id, floatingip_id=floatingip_id)
            esnat_object = esnat_objects[0] if esnat_objects else None
            if not esnat_object:
                return
            cidrs = self._get_snat_cidrs(context, esnat_object)
            cidrs = sorted(cidrs, key=lambda x: x[-2:])
            for cidr in cidrs:
                for direction in const_lib.VALID_DIRECTIONS:
                    rule = self._build_metering_label_rule_dict(
                        cidr, direction, label_id,
                        constants.METERING_TYPE_SNAT_EIP, False)
                    data = self.get_sync_data_for_rule(
                        context, rule['metering_label_rule'])
                    LOG.info('label:%s, rule:%s', label_id, rule)
                    self.meter_rpc.add_metering_label_rule(context, data)

    def _delete_snat_meter_rule(self, context, label_id):
        filters = {'metering_label_id': label_id}
        meter_rules = self.get_metering_label_rules(context, filters=filters)
        for rule in meter_rules:
            self.delete_metering_label_rule(context, rule['id'])

    def _delete_snat_meter_rules_async(self, context, router_id, label_id):
        """Process to delete all meter label rules quickly

        1.delete all meter rules by label_id in db
        2.send routers_update rpc to clean metering_agent iptables(ovs) rules
        """
        LOG.debug("Delete snat meter rules async, router:%s, label:%s.",
                  router_id, label_id)
        if not router_id or not label_id:
            return
        router = {'router_id': router_id,
                  'label_id': label_id,
                  'action': 'clean',
                  'label_type': constants.METERING_TYPE_SNAT_EIP}
        metering_objs.MeteringLabelRule.delete_objects(
            context, metering_label_id=label_id,
            type=constants.METERING_TYPE_SNAT_EIP)
        self.meter_rpc.update_metering(context, router=router)

    def create_snat_meter(self, resource, event, trigger, **kwargs):
        esnat_obj, context = self._get_esnat_obj(kwargs)
        if not esnat_obj:
            return
        lable_filters = {'floatingip_id': esnat_obj.floatingip_id}
        exist_label = self.get_metering_labels(context, lable_filters)
        if not exist_label:
            try:
                label = self._create_snat_meter_label(context, esnat_obj)
                self._create_snat_meter_rules_async(context,
                                                    esnat_obj, label['id'])
            except Exception as e:
                LOG.error('Create snat meter failed cause %s', e)
        else:
            label = exist_label[0]
            label_type = label['type']

            if label_type in (constants.METERING_TYPE_EIP,
                              constants.METERING_TYPE_ECS_EIP):
                LOG.error('Floatingip %s label type is not pf_eip',
                          esnat_obj.floatingip_id)
                return
            elif label_type == constants.METERING_TYPE_PF_EIP:
                label['type'] = constants.METERING_TYPE_MULTI_EIP
                self._update_metering_label_type(context, label['id'], label)
                self._create_snat_meter_rules_async(
                    context, esnat_obj, label['id'])
            elif label_type == constants.METERING_TYPE_MULTI_EIP:
                rule_filter = {'metering_label_id': label['id'],
                               'type': constants.METERING_TYPE_SNAT_EIP}
                rules = self.get_metering_label_rules(context, rule_filter)
                if rules:
                    for rule in rules:
                        self.delete_metering_label_rule(context,
                                                        rule['id'])
                self._create_snat_meter_rules_async(
                    context, esnat_obj, label['id'])

    def update_snat_meter(self, resource, event, trigger, **kwargs):
        esnat_obj, context = self._get_esnat_obj(kwargs)
        if not esnat_obj:
            return
        # Parameter initialization
        floatingip_id = esnat_obj['floatingip_id']
        filters = {'floatingip_id': floatingip_id}
        labels = self.get_metering_labels(context, filters=filters)

        if not labels:
            label = self._create_snat_meter_label(context, esnat_obj)
            self._create_snat_meter_rules_async(context, esnat_obj,
                                                label['id'])
        else:
            label_id = labels[0]['id']
            label_type = labels[0]['type']
            if label_type == constants.METERING_TYPE_SNAT_EIP:
                self._delete_snat_meter_rules_async(
                    context, esnat_obj.router_id, label_id)
                self._create_snat_meter_rules_async(
                    context, esnat_obj, label_id)
            elif label_type == constants.METERING_TYPE_MULTI_EIP:
                self._delete_snat_meter_rules_async(
                    context, esnat_obj.router_id, label_id)
                self._create_snat_meter_rules_async(context, esnat_obj,
                                                    label_id)

    def delete_snat_meter(self, resource, event, trigger, **kwargs):
        esnat_obj, context = self._get_esnat_obj(kwargs)
        if not esnat_obj:
            return
        floatingip_id = esnat_obj['floatingip_id']
        filters = {'floatingip_id': floatingip_id}
        labels = self.get_metering_labels(context, filters=filters)
        if not labels:
            return
        label_type = labels[0]['type']
        if label_type == constants.METERING_TYPE_SNAT_EIP:
            self._delete_fip_meter(context, floatingip_id)
        elif label_type == constants.METERING_TYPE_MULTI_EIP:
            r_filter = {'type': constants.METERING_TYPE_SNAT_EIP,
                        'metering_label_id': labels[0]['id']}
            rules = self.get_metering_label_rules(context, filters=r_filter)
            labels[0]['type'] = constants.METERING_TYPE_PF_EIP
            self._update_metering_label_type(context, labels[0]['id'],
                                             labels[0])
            for rule in rules:
                self.delete_metering_label_rule(context, rule['id'])

            pf_rules = self.get_metering_label_rules(
                context, filters={'metering_label_id': labels[0]['id']})
            if not pf_rules:
                self.delete_metering_label(context, labels[0]['id'])

    def _get_snat_cidrs(self, context, esnat_obj):
        cidrs = set()
        internal_cidrs = esnat_obj.get('internal_cidrs', [])
        subnets = esnat_obj.get('subnets', [])
        # Sort cidrs by priority
        cidrs = self._sort_esnat_cidr(context, internal_cidrs, subnets)
        return cidrs
