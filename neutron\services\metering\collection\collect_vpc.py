# -*- coding: utf-8 -*-
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import socket

from oslo_log import log as logging
from oslo_serialization import jsonutils
from oslo_utils import timeutils

from neutron.agent.linux import ip_lib
from neutron.services.metering.agents import kafka_manager as kafka_mgt

LOG = logging.getLogger(__name__)

WARNING_VPC_REPORT_TIME_THREASHOLD = 300


class MonitorVPC(object):
    def __init__(self):
        LOG.debug('MonitorVPC init')
        self.first_update = 0
        self.last_update = 0
        self.metering_vpcs = []
        self.hostname = socket.gethostname()
        self.KafkaManager = kafka_mgt.Vpc2VpcKafkaManager()

    def __del__(self):
        LOG.debug('MonitorVPC destory')

    def get_ha_interface_in_namespace(self, ns):
        ip_wrapper = ip_lib.IPWrapper(namespace=ns)
        ip_devs = ip_wrapper.get_devices()
        ha_device = []
        for ip_dev in ip_devs:
            if 'ha-' in ip_dev.name:
                ha_device = ip_dev.name
        return ha_device

    def _get_router_role(self, device_name, namespace):
        try:
            device = ip_lib.IPDevice(device_name, namespace=namespace)
            inc = 0
            device_ip_cidrs = [ip['cidr'] for ip in device.addr.list()]
            for ip_cidr in device_ip_cidrs:
                ipseg = ip_cidr.strip().split('.')
                if ipseg[0] == '169' and ipseg[1] == '254':
                    inc += 1
                LOG.debug('parsing ha ip_cidr= %s inc=%s', ip_cidr, inc)
            if inc == 2:
                return 'master'
        except RuntimeError:
            return 'slave'
        else:
            return 'slave'

    def ping_destination(self, meter_vpc, network_id=''):
        # Ping destionation from router and collect result
        if network_id:
            net_ns = 'qdhcp-%s' % (network_id.encode())
        else:
            net_ns = 'qrouter-%s' % (meter_vpc['router_id'].encode())
        ip_wrapper = ip_lib.IPWrapper(namespace=net_ns)
        destination_ip = meter_vpc['destination_ip'].encode()
        if ":" not in meter_vpc['destination_ip']:
            ping_cmd = ['ping', '-c', '3', '-w', '3', destination_ip]
        else:
            ping_cmd = ['ping6', '-c', '3', '-w', '3', destination_ip]
        try:
            result = ip_wrapper.netns.execute(ping_cmd,
                                              check_exit_code=False,
                                              return_stderr=True,
                                              log_fail_as_error=False)
            result = (result[0].encode()).splitlines()

            if len(result) > 1:
                res_end = []
                avg_delay = '0 ms'
                for res in result:
                    if 'packets transmitted' in res:
                        res_end = (res.replace(' ', '')).split(',')
                        continue
                    elif 'rtt' in res:
                        res_avg_delay = (
                            ((res.replace(' ',
                                          '')).split('='))[-1]).split('/')[1]
                        time_unit = res.split(' ')[-1]
                        avg_delay = res_avg_delay + ' ' + time_unit
                        continue
                if res_end:
                    for i in res_end:
                        if 'received' in i:
                            received = i.split('received')[0]
                            continue
                        elif 'packetloss' in i:
                            packetloss = i.split('packetloss')[0]
                            continue
                        else:
                            continue

                else:
                    received = '0'
                    packetloss = '100%'
                ping_result = {'packets_transmitted': '3',
                               'received': received,
                               'packet_loss': packetloss,
                               'avg_delay': avg_delay}
                return ping_result
            else:
                result = {'packets_transmitted': '3',
                          'received': '0',
                          'packet_loss': '100%',
                          'avg_delay': '0 ms'}
                return result
        except Exception as e:
            LOG.error('Execute command %s failed, cause %s', ping_cmd, e)
            result = {'packets_transmitted': '3',
                      'received': '0',
                      'packet_loss': '100%',
                      'avg_delay': '0 ms'}
            return result

    def ping_destination_result(self, meter_vpc, network_id=''):
        timestamp = timeutils.utcnow_ts(microsecond=True)
        result = self.ping_destination(meter_vpc, network_id)
        return {'result': result,
                'network_id': network_id,
                'timestamp': timestamp,
                'router_id': meter_vpc['router_id'],
                'destination_ip': meter_vpc['destination_ip'],
                'dest_device_uuid': meter_vpc['dest_device_uuid'],
                'type': meter_vpc['type'],
                'hostname': self.hostname}

    def _handle_result(self, metering_vpcs, metering_vpc_results,
                       vpc_log_handle, mtype):
        try:
            if metering_vpc_results:
                # Save log local
                vpc_to_peer_res = {'topic_vpc2vpc': metering_vpc_results}

                # Send the result to kafka
                vpc_to_peer_res_json = jsonutils.dumps(
                    vpc_to_peer_res, ensure_ascii=False, indent=1)

                self._sent_to_kafka('producer_monitor_topic_vpc2vpc',
                                    vpc_to_peer_res_json)
                if mtype == 'dhcpns':
                    self.KafkaManager.vpc2vpc_dhcp_counter += 1
                elif mtype == 'routerns':
                    self.KafkaManager.vpc2vpc_rns_counter += 1

        except Exception as e:
            LOG.error('Writing router vpc to peer ip '
                      'logfile failed...e=%s', e)
        return {'metering_vpc': metering_vpcs,
                'metering_vpc_results': metering_vpc_results}

    def _get_all_dhcps(self):
        all_dhcps = []
        namespaces = ip_lib.list_network_namespaces()
        for ns in namespaces:
            if ns.startswith('qdhcp-'):
                all_dhcps.append((ns.split(' ')[0]).split('qdhcp-')[-1])
        if not all_dhcps:
            LOG.warning('This node has no dhcp namespace.')
        return all_dhcps

    def _metering_vpc_to_vpc_by_dhcp_ns(
            self, metering_vpcs, vpc_log_handle):
        all_dhcps = self._get_all_dhcps()
        metering_vpc_results = []
        for meter_vpc in metering_vpcs:
            for network_id in meter_vpc['network_id']:
                if network_id in all_dhcps:
                    vpc_res = self.ping_destination_result(
                        meter_vpc, network_id)
                    metering_vpc_results.append(vpc_res)
        return self._handle_result(metering_vpcs, metering_vpc_results,
                                   vpc_log_handle, 'dhcpns')

    def monitor_metering_vpc_to_vpc(
            self, log_handle, metering_vpcs):
        """
        :param log_handle: local log handler
        :param metering_vpc: resources of vpc
        :return: connection result from router to destionation.
        """
        LOG.debug('Entry collect VPC2VPC')

        LOG.info('vpc2vpc_dhcp_counter=%s vpc2vpc_rns_counter=%s',
                 self.KafkaManager.vpc2vpc_dhcp_counter,
                 self.KafkaManager.vpc2vpc_rns_counter)
        if metering_vpcs and metering_vpcs[0].get('network_id'):
            self._metering_vpc_to_vpc_by_dhcp_ns(
                metering_vpcs, log_handle)
        else:
            # Ping destionation ip from router namespace
            self._metering_vpc_to_vpc_by_router_ns(
                metering_vpcs, log_handle)
        LOG.debug('Exit collect VPC2VPC')

    def _refresh_route_ns(self, all_routers):
        master_router = []
        slave_router = []
        # Get master router
        if all_routers:
            for router in all_routers:
                ns = 'qrouter-' + router
                ha_device_name = self.get_ha_interface_in_namespace(ns)
                if ha_device_name:
                    router_role = self._get_router_role(ha_device_name, ns)
                    if 'master' == router_role:
                        master_router.append(router)
                    else:
                        slave_router.append(router)
        ha_routers = {'master': master_router,
                      'slave': slave_router}
        return ha_routers

    def _metering_vpc_to_vpc_by_router_ns(
            self, metering_vpcs, vpc_log_handle):
        # Get all router namespaces
        all_routers = self._get_all_routers()
        # Get master route on host
        ha_routers = self._refresh_route_ns(all_routers)
        metering_vpc_results = []
        if metering_vpcs:
            for meter_vpc in metering_vpcs:
                if meter_vpc['ha']:
                    if meter_vpc['router_id'] in ha_routers['master']:
                        vpc_res = self.ping_destination_result(
                            meter_vpc)
                        metering_vpc_results.append(vpc_res)
                else:
                    if meter_vpc['router_id'] in all_routers:
                        vpc_res = self.ping_destination_result(
                            meter_vpc)
                        metering_vpc_results.append(vpc_res)
        return self._handle_result(metering_vpcs, metering_vpc_results,
                                   vpc_log_handle, 'routerns')

    def _sent_to_kafka(self, producer_key, metering_vpc_result):
        # to kafka
        LOG.debug('\n ===vpc2vpc to kafka===:%s', metering_vpc_result)
        self.KafkaManager.kafka_dict[producer_key].produce(
            bytes(metering_vpc_result))

    def _get_all_routers(self):
        all_routers_list = []
        namespaces = ip_lib.list_network_namespaces()
        for ns in namespaces:
            if ns.startswith('qrouter'):
                all_routers_list.append(ns)
        all_routers = []
        if not all_routers_list:
            LOG.warning('This code has no router namespace.')
            return
        for router in all_routers_list:
            all_routers.append((router.split(' ')[0]).split('qrouter-')[-1])
        set(all_routers)
        return all_routers
