# -*- coding: utf-8 -*-
# <AUTHOR> <EMAIL>
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.

import os
import socket

import eventlet
from oslo_config import cfg
from oslo_log import log as logging
from oslo_serialization import jsonutils
from oslo_utils import timeutils
from pykafka import exceptions as kafka_exc

from neutron.agent.linux import ip_lib


LOG = logging.getLogger(__name__)
IP_STEP_LEN = 500


class MonitorVm(object):
    def __init__(self, vmKafkaManager):
        self.hostname = socket.gethostname()
        self.KafkaManager = vmKafkaManager
        self.vm_health_counter = 0
        self.vm_num = 0

    def monitor_resource_vm(self, monitor_log):
        pool_ping = eventlet.GreenPool(size=50)
        dhcp_file_dir = os.path.dirname("%s/" % cfg.CONF.state_path) + '/dhcp/'
        namespaces = os.listdir(dhcp_file_dir)
        if not namespaces:
            LOG.info('There is no dhcp namespace at the node.')
            return
        else:
            try:
                for ns_id in namespaces:
                    host_file = dhcp_file_dir + ns_id + '/host'
                    if (not os.path.exists(host_file)) \
                            or (not os.path.exists(dhcp_file_dir + ns_id)):
                        LOG.warning('dhcp namespace %s is not exist on'
                                    ' the %s node', ns_id, self.hostname)
                        continue
                    pool_ping.spawn_n(self._monitor_resource_vm, ns_id,
                                      host_file, monitor_log)

            except Exception as e:
                LOG.error('Failed to monitor dhcp vm, because %s', e)

    def _report_ping_result(self, monitor_vm_result):
        producer_key = 'producer_monitor_topic_vm_healthchk'
        vm_body_str = {'topic_vm_healthchk': monitor_vm_result}
        try:
            _build_ping_result = jsonutils.dumps(
                vm_body_str, ensure_ascii=False, indent=1)

            LOG.debug("Reporting Vm counter=%s", vm_body_str)
            self.KafkaManager.kafka_dict[producer_key].produce(
                bytes(_build_ping_result))

            self.vm_health_counter += 1

        except (kafka_exc.SocketDisconnectedError,
                kafka_exc.LeaderNotAvailable):
            LOG.warning("Kafka connection has lost, "
                        "reconnect and resending...")

            self.KafkaManager.handle_loss_connection(
                producer_key, _build_ping_result)

    def _build_ping_result(self, outcome, vm_dict, vm_result):
        for res in outcome:
            result = {}
            res_list = res.split(' is ')
            result['type'] = 'vm'
            result['vm_ip'] = res_list[0]
            result['result'] = res_list[-1]
            novauuid = vm_dict.get(res_list[0])
            result['nova_uuid'] = novauuid
            vm_result.append(result)
            self.vm_num += 1

    def _monitor_resource_vm(self, ns_id, host_file, monitor_log):
        # Parsing ip address and nova_uuid
        # list of ip and nova id info about port
        vm_dict = {}
        # ping result of vm_list
        vm_result = []
        # list of vm port info
        vm_ports = []
        # vm struce_list
        ip_list = []
        try:
            with open(host_file, 'rb') as hostfile:
                for h_line in hostfile:
                    if 'nova-' in h_line:
                        h_line = h_line.replace('\n', '')
                        h_line = h_line.replace(' ', '')
                        vm_ports.append(h_line)
        except Exception as e:
            LOG.error('Failed to parsing host file in namespace %s'
                      ' and error info is %s', ns_id, e)
        for port_info in vm_ports:
            # get nova uuid
            nova_uuid = port_info.split('nova-')[-1]
            interface_info = (
                (port_info.split('nova-')[0]).strip(',')).split(',')
            if interface_info.__len__() == 3:
                ipaddr = interface_info[2]
            elif interface_info.__len__() == 4:
                if 'set:' in interface_info[-1]:
                    ipaddr = interface_info[2]
                else:
                    ipaddr = interface_info[3]
            elif interface_info.__len__() == 5:
                ipaddr = interface_info[3]
            if ':' in ipaddr:
                ipaddr = ipaddr.split('[')[-1].split(']')[0]
            vm_dict[ipaddr] = nova_uuid
            ip_list.append(ipaddr)

        fping_cmd = []
        ns_name = 'qdhcp-' + ns_id
        ip_wrapper = ip_lib.IPWrapper(namespace=ns_name)

        for i in range(0, len(ip_list), IP_STEP_LEN):
            ip_seg = list(ip_list)[i:i + IP_STEP_LEN]
            fping_cmd = ['fping', '-r', 1] + ip_seg
            outcome = []
            if fping_cmd:
                try:
                    outcome = ip_wrapper.netns.execute(
                        fping_cmd, check_exit_code=False, return_stderr=True,
                        log_fail_as_error=False)
                    outcome = (outcome[0].encode()).splitlines()
                except Exception as e:
                    LOG.error('Failed to exec fping cmd, cause %s', e)

                if outcome:
                    self._build_ping_result(outcome, vm_dict, vm_result)

        if vm_result:
            timestamp = timeutils.utcnow_ts()
            monitor_vm_result = {'hostname': self.hostname,
                                 'network': ns_id,
                                 'timestamp': timestamp,
                                 'vm_accessibility': vm_result
                                 }

            self._report_ping_result(monitor_vm_result)
            LOG.info('Vm loop report, counter=%s  vm num=%s',
                     self.vm_health_counter, self.vm_num)
